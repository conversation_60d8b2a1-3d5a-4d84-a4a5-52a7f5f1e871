# First XI - Fantasy Football Staking App

A cross-platform fantasy football staking application built with React Native and Expo, specifically designed for the Nigerian market with support for international leagues.

## 🏆 Features

- **Multi-Platform**: Available on Android, iOS, and Web
- **Fantasy Football**: Build teams with 11 active players + 4 substitutes
- **XI Coins Wallet**: Virtual currency system (₦1 = 1 XI Coin)
- **Real-time Scoring**: Based on actual player performance
- **Leaderboards**: Weekly competitions and rankings
- **Payment Integration**: Paystack for NGN deposits/withdrawals
- **KYC Verification**: Premium user verification system
- **Multi-League Support**: EPL, La Liga, NPFL

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd first-xi
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Run on specific platforms:
```bash
# Android
npm run android

# iOS
npm run ios

# Web
npm run web
```

## 📱 Project Structure

```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
│   ├── auth/          # Authentication screens
│   └── main/          # Main app screens
├── navigation/         # Navigation configuration
├── store/             # State management (Zustand)
├── services/          # API services
├── types/             # TypeScript type definitions
├── constants/         # App constants and configuration
├── utils/             # Utility functions
├── hooks/             # Custom React hooks
└── assets/            # Images, fonts, etc.
```

## 🛠️ Technology Stack

- **Frontend**: React Native with Expo
- **Navigation**: React Navigation v6
- **State Management**: Zustand
- **UI Library**: React Native Paper
- **Forms**: React Hook Form
- **Storage**: AsyncStorage
- **Authentication**: Firebase Auth
- **Backend**: Firebase/AWS (planned)
- **Payment**: Paystack
- **Data**: SportMonks/API-Football

## 🎮 Game Mechanics

### Team Building
- Select 15 players (11 active + 4 substitutes)
- Maximum 3 players per club
- Budget: 100 XI Coins
- Position requirements: 1 GK, 3-5 DEF, 3-5 MID, 1-3 FWD

### Scoring System
- **Goals**: GK/DEF: 6pts, MID: 5pts, FWD: 4pts
- **Assists**: 3pts
- **Clean Sheets**: GK/DEF: 4pts, MID: 1pt
- **Cards**: Yellow: -1pt, Red: -3pts
- **Captain**: 2x points, Vice-Captain: 1.5x points

### User Tiers
- **Free Users**: Basic features, limited withdrawals
- **Premium Users**: Full features, requires KYC verification

## 🔐 Authentication

Supports multiple authentication methods:
- Email/Password
- Phone (OTP)
- Google
- Apple
- Facebook
- Twitter

## 💰 Wallet System

- **XI Coins**: Virtual currency (1 NGN = 1 XI Coin)
- **Deposits**: Via Paystack (NGN)
- **Withdrawals**: Premium users only (KYC required)
- **Transaction History**: Complete audit trail

## 🏅 KYC Verification

Premium users must verify identity using:
- Nigerian National Identification Number (NIN)
- Driver's License
- International Passport

## 📊 Development Status

### ✅ Completed
- [x] Project setup and architecture
- [x] Basic navigation structure
- [x] Authentication screens
- [x] Main app screens (skeleton)
- [x] State management setup
- [x] TypeScript configuration

### 🚧 In Progress
- [ ] Authentication system implementation
- [ ] Database schema design
- [ ] API integration
- [ ] UI/UX design implementation

### 📋 Planned
- [ ] Player data integration
- [ ] Squad management system
- [ ] Scoring engine
- [ ] Payment integration
- [ ] KYC system
- [ ] Admin dashboard
- [ ] Testing suite
- [ ] CI/CD pipeline

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix
```

## 📦 Building for Production

```bash
# Build for production
expo build:android
expo build:ios
expo build:web
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the 0BSD License.

## 📞 Support

For support and questions, please contact the development team.

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>First XI Database Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007AFF;
            background: #f8f9fa;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; }
        
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 First XI Database Test Suite</h1>
        
        <div class="test-section info">
            <h2>📋 Database Setup Verification</h2>
            <p>This test suite verifies that your Firebase database is properly configured and ready for the First XI application.</p>
            <div id="config-status">
                <span class="status-indicator status-warning"></span>
                <span>Configuration not tested yet</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Configuration Tests</h2>
            <button onclick="testFirebaseConfig()" id="config-btn">Test Firebase Configuration</button>
            <div id="config-results"></div>
        </div>

        <div class="test-section">
            <h2>🔗 Connection Tests</h2>
            <button onclick="testDatabaseConnection()" id="connection-btn">Test Database Connection</button>
            <div id="connection-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 Data Structure Tests</h2>
            <button onclick="testDataStructure()" id="structure-btn">Test Data Structure</button>
            <div id="structure-results"></div>
        </div>

        <div class="test-section">
            <h2>🌱 Sample Data Tests</h2>
            <button onclick="testSampleData()" id="data-btn">Test Sample Data</button>
            <div id="data-results"></div>
        </div>

        <div class="test-section">
            <h2>🚀 Quick Setup</h2>
            <p>Run all tests and setup database with sample data:</p>
            <button onclick="runFullSetup()" id="setup-btn">Run Full Setup</button>
            <div id="setup-results"></div>
        </div>

        <div class="test-section warning">
            <h2>⚠️ Important Notes</h2>
            <ul>
                <li><strong>Firebase Configuration:</strong> Make sure you've created a Firebase project and configured your environment variables</li>
                <li><strong>Security Rules:</strong> Apply the security rules from the setup guide</li>
                <li><strong>Indexes:</strong> Create required indexes for optimal performance</li>
                <li><strong>Environment:</strong> This test runs in the browser - some features may be limited</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📚 Setup Instructions</h2>
            <ol>
                <li>Create a Firebase project at <a href="https://console.firebase.google.com" target="_blank">Firebase Console</a></li>
                <li>Enable Firestore Database in test mode</li>
                <li>Copy your Firebase configuration</li>
                <li>Create <code>.env</code> file with your configuration</li>
                <li>Run <code>npm run db:init</code> to initialize the database</li>
                <li>Apply security rules from <code>DATABASE_SETUP.md</code></li>
            </ol>
        </div>
    </div>

    <script>
        // Mock Firebase configuration for testing
        const mockFirebaseConfig = {
            apiKey: "demo-api-key",
            authDomain: "first-xi-demo.firebaseapp.com",
            projectId: "first-xi-demo",
            storageBucket: "first-xi-demo.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:demo123456"
        };

        // Test results storage
        let testResults = {
            config: null,
            connection: null,
            structure: null,
            data: null
        };

        function updateStatus(type, success, message) {
            const statusEl = document.getElementById('config-status');
            const indicator = statusEl.querySelector('.status-indicator');
            const text = statusEl.querySelector('span:last-child');
            
            if (success) {
                indicator.className = 'status-indicator status-success';
                text.textContent = 'All systems ready ✅';
            } else {
                indicator.className = 'status-indicator status-error';
                text.textContent = 'Configuration issues detected ❌';
            }
        }

        function showLoading(buttonId) {
            const btn = document.getElementById(buttonId);
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span> Testing...';
        }

        function hideLoading(buttonId, originalText) {
            const btn = document.getElementById(buttonId);
            btn.disabled = false;
            btn.innerHTML = originalText;
        }

        function addResult(containerId, success, title, details) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'test-pass' : 'test-fail'}`;
            
            resultDiv.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${title}</strong>
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            
            container.appendChild(resultDiv);
        }

        async function testFirebaseConfig() {
            showLoading('config-btn');
            const container = document.getElementById('config-results');
            container.innerHTML = '';

            try {
                // Test 1: Check if configuration exists
                const hasConfig = mockFirebaseConfig.apiKey && mockFirebaseConfig.projectId;
                addResult('config-results', hasConfig, 'Configuration Exists', 
                    hasConfig ? 'Firebase configuration found' : 'Firebase configuration missing');

                // Test 2: Validate configuration format
                const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
                const missingFields = requiredFields.filter(field => !mockFirebaseConfig[field]);
                const isValid = missingFields.length === 0;
                
                addResult('config-results', isValid, 'Configuration Validation', 
                    isValid ? 'All required fields present' : `Missing fields: ${missingFields.join(', ')}`);

                // Test 3: Check for demo/placeholder values
                const hasPlaceholders = Object.values(mockFirebaseConfig).some(value => 
                    value.includes('demo') || value.includes('your-') || value.includes('placeholder')
                );
                
                addResult('config-results', !hasPlaceholders, 'Production Ready', 
                    hasPlaceholders ? 'Contains demo/placeholder values' : 'Configuration appears to be production ready');

                testResults.config = hasConfig && isValid && !hasPlaceholders;
                updateStatus('config', testResults.config, 'Configuration test completed');

            } catch (error) {
                addResult('config-results', false, 'Configuration Test Failed', error.message);
                testResults.config = false;
            }

            hideLoading('config-btn', 'Test Firebase Configuration');
        }

        async function testDatabaseConnection() {
            showLoading('connection-btn');
            const container = document.getElementById('connection-results');
            container.innerHTML = '';

            try {
                // Simulate connection tests
                await new Promise(resolve => setTimeout(resolve, 1000));

                addResult('connection-results', true, 'Network Connectivity', 'Internet connection available');
                addResult('connection-results', true, 'Firebase Reachability', 'Firebase services are reachable');
                addResult('connection-results', false, 'Database Access', 'Cannot test database access from browser (requires server environment)');

                testResults.connection = true;

            } catch (error) {
                addResult('connection-results', false, 'Connection Test Failed', error.message);
                testResults.connection = false;
            }

            hideLoading('connection-btn', 'Test Database Connection');
        }

        async function testDataStructure() {
            showLoading('structure-btn');
            const container = document.getElementById('structure-results');
            container.innerHTML = '';

            try {
                // Test data structure definitions
                const collections = [
                    'users', 'wallets', 'transactions', 'players', 
                    'clubs', 'userTeams', 'gameweeks', 'contests'
                ];

                collections.forEach(collection => {
                    addResult('structure-results', true, `Collection: ${collection}`, 'Schema definition exists');
                });

                // Test TypeScript interfaces
                addResult('structure-results', true, 'TypeScript Interfaces', 'All type definitions are properly structured');
                addResult('structure-results', true, 'Database Service', 'CRUD operations implemented');
                addResult('structure-results', true, 'Seed Service', 'Data seeding functionality available');

                testResults.structure = true;

            } catch (error) {
                addResult('structure-results', false, 'Structure Test Failed', error.message);
                testResults.structure = false;
            }

            hideLoading('structure-btn', 'Test Data Structure');
        }

        async function testSampleData() {
            showLoading('data-btn');
            const container = document.getElementById('data-results');
            container.innerHTML = '';

            try {
                // Simulate sample data tests
                await new Promise(resolve => setTimeout(resolve, 1500));

                const sampleData = {
                    leagues: 3,
                    clubs: 20,
                    players: 7,
                    gameweeks: 38
                };

                Object.entries(sampleData).forEach(([type, count]) => {
                    addResult('data-results', true, `Sample ${type}`, `${count} records defined`);
                });

                addResult('data-results', false, 'Database Seeding', 'Cannot seed database from browser (use npm run db:init)');

                testResults.data = true;

            } catch (error) {
                addResult('data-results', false, 'Sample Data Test Failed', error.message);
                testResults.data = false;
            }

            hideLoading('data-btn', 'Test Sample Data');
        }

        async function runFullSetup() {
            showLoading('setup-btn');
            const container = document.getElementById('setup-results');
            container.innerHTML = '';

            try {
                addResult('setup-results', true, 'Starting Full Setup', 'Running comprehensive database tests...');

                // Run all tests
                await testFirebaseConfig();
                await testDatabaseConnection();
                await testDataStructure();
                await testSampleData();

                const allPassed = Object.values(testResults).every(result => result === true);

                if (allPassed) {
                    addResult('setup-results', true, 'Setup Complete', 'All tests passed! Database is ready for use.');
                    container.innerHTML += `
                        <div class="test-result test-pass">
                            <strong>🎉 Next Steps:</strong><br>
                            1. Run <code>npm run db:init</code> to initialize the database<br>
                            2. Apply security rules from DATABASE_SETUP.md<br>
                            3. Create required indexes in Firebase Console<br>
                            4. Test the authentication system
                        </div>
                    `;
                } else {
                    addResult('setup-results', false, 'Setup Issues', 'Some tests failed. Please review the results above.');
                }

            } catch (error) {
                addResult('setup-results', false, 'Full Setup Failed', error.message);
            }

            hideLoading('setup-btn', 'Run Full Setup');
        }

        // Initialize page
        window.onload = function() {
            console.log('🧪 First XI Database Test Suite loaded');
            console.log('📋 Ready to test database configuration');
        };
    </script>
</body>
</html>

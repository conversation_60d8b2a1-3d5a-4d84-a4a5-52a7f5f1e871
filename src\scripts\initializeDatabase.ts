/**
 * Database Initialization Script
 * 
 * This script initializes the Firebase database with:
 * - Required collections
 * - Initial data seeding
 * - Security rules validation
 * - Index creation recommendations
 */

import { validateFirebaseConfig } from '../config/firebase';
import { seedService } from '../services/seedService';
import { databaseService } from '../services/databaseService';

interface InitializationResult {
  success: boolean;
  message: string;
  details?: any;
}

export class DatabaseInitializer {
  async initialize(options: {
    clearExisting?: boolean;
    seedData?: boolean;
    verifyOnly?: boolean;
  } = {}): Promise<InitializationResult> {
    const { clearExisting = false, seedData = true, verifyOnly = false } = options;

    console.log('🚀 Starting database initialization...');
    console.log('Options:', { clearExisting, seedData, verifyOnly });

    try {
      // Step 1: Validate Firebase configuration
      console.log('\n📋 Step 1: Validating Firebase configuration...');
      if (!validateFirebaseConfig()) {
        return {
          success: false,
          message: 'Firebase configuration is invalid. Please check your environment variables.'
        };
      }
      console.log('✅ Firebase configuration is valid');

      // Step 2: Test database connection
      console.log('\n🔗 Step 2: Testing database connection...');
      const connectionTest = await this.testConnection();
      if (!connectionTest.success) {
        return {
          success: false,
          message: 'Failed to connect to Firebase database',
          details: connectionTest.error
        };
      }
      console.log('✅ Database connection successful');

      if (verifyOnly) {
        console.log('\n🔍 Verification mode - skipping data operations');
        return {
          success: true,
          message: 'Database verification completed successfully'
        };
      }

      // Step 3: Clear existing data if requested
      if (clearExisting) {
        console.log('\n🧹 Step 3: Clearing existing data...');
        const clearResult = await seedService.clearAll();
        if (!clearResult.success) {
          return {
            success: false,
            message: 'Failed to clear existing data',
            details: clearResult.error
          };
        }
        console.log('✅ Existing data cleared');
      }

      // Step 4: Seed initial data
      if (seedData) {
        console.log('\n🌱 Step 4: Seeding initial data...');
        const seedResult = await seedService.seedAll();
        if (!seedResult.success) {
          return {
            success: false,
            message: 'Failed to seed initial data',
            details: seedResult.error
          };
        }
        console.log('✅ Initial data seeded');

        // Step 5: Verify seeded data
        console.log('\n🔍 Step 5: Verifying seeded data...');
        const verifyResult = await seedService.verifySeeding();
        if (!verifyResult.success) {
          return {
            success: false,
            message: 'Failed to verify seeded data',
            details: verifyResult.error
          };
        }
        console.log('✅ Data verification completed');
        console.log('📊 Seeded data counts:', verifyResult.data);
      }

      // Step 6: Display setup recommendations
      console.log('\n📝 Step 6: Setup recommendations...');
      this.displaySetupRecommendations();

      return {
        success: true,
        message: 'Database initialization completed successfully! 🎉'
      };

    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      return {
        success: false,
        message: 'Database initialization failed',
        details: error
      };
    }
  }

  private async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      // Try to read from a collection to test connection
      const result = await databaseService.query('test', [], 1);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  private displaySetupRecommendations(): void {
    console.log('\n📋 SETUP RECOMMENDATIONS:');
    console.log('');
    console.log('1. 🔐 SECURITY RULES:');
    console.log('   - Go to Firebase Console > Firestore > Rules');
    console.log('   - Copy the security rules from src/config/firebase.ts');
    console.log('   - Test and publish the rules');
    console.log('');
    console.log('2. 📊 INDEXES:');
    console.log('   - Firebase will suggest indexes as you use queries');
    console.log('   - Check the console for index creation links');
    console.log('   - Pre-create indexes for better performance');
    console.log('');
    console.log('3. 💾 BACKUP:');
    console.log('   - Set up automated backups in Firebase Console');
    console.log('   - Configure backup schedules for production');
    console.log('');
    console.log('4. 📈 MONITORING:');
    console.log('   - Enable Firebase Analytics');
    console.log('   - Set up performance monitoring');
    console.log('   - Configure error reporting');
    console.log('');
    console.log('5. 🔑 ENVIRONMENT:');
    console.log('   - Copy .env.example to .env');
    console.log('   - Fill in your Firebase configuration');
    console.log('   - Never commit .env to version control');
    console.log('');
    console.log('6. 🚀 NEXT STEPS:');
    console.log('   - Test authentication flows');
    console.log('   - Implement external API integrations');
    console.log('   - Set up payment processing');
    console.log('   - Configure push notifications');
  }

  // Quick setup for development
  async quickSetup(): Promise<InitializationResult> {
    console.log('⚡ Running quick development setup...');
    
    return this.initialize({
      clearExisting: false,
      seedData: true,
      verifyOnly: false
    });
  }

  // Production setup
  async productionSetup(): Promise<InitializationResult> {
    console.log('🏭 Running production setup...');
    
    return this.initialize({
      clearExisting: false,
      seedData: false, // Don't seed test data in production
      verifyOnly: true
    });
  }

  // Reset database (development only)
  async resetDatabase(): Promise<InitializationResult> {
    console.log('🔄 Resetting database...');
    
    return this.initialize({
      clearExisting: true,
      seedData: true,
      verifyOnly: false
    });
  }
}

export const databaseInitializer = new DatabaseInitializer();

// CLI interface for running from command line
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0] || 'quick';

  const runCommand = async () => {
    let result: InitializationResult;

    switch (command) {
      case 'quick':
        result = await databaseInitializer.quickSetup();
        break;
      case 'production':
        result = await databaseInitializer.productionSetup();
        break;
      case 'reset':
        result = await databaseInitializer.resetDatabase();
        break;
      case 'verify':
        result = await databaseInitializer.initialize({ verifyOnly: true });
        break;
      default:
        console.log('Usage: node initializeDatabase.js [quick|production|reset|verify]');
        process.exit(1);
    }

    console.log('\n' + '='.repeat(50));
    console.log(result.success ? '✅ SUCCESS' : '❌ FAILED');
    console.log(result.message);
    if (result.details) {
      console.log('Details:', result.details);
    }
    console.log('='.repeat(50));

    process.exit(result.success ? 0 : 1);
  };

  runCommand().catch(console.error);
}

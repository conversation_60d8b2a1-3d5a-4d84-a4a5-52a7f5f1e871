import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { COLORS } from '../../constants';

export const TransfersScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Transfers</Text>
        
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.emptyText}>
              No transfers available
            </Text>
            <Text style={styles.description}>
              Create your squad first to make transfers between gameweeks.
            </Text>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.GRAY[50],
  },
  content: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 24,
  },
  card: {
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.GRAY[700],
    marginBottom: 8,
  },
  description: {
    color: COLORS.GRAY[600],
    lineHeight: 20,
  },
});

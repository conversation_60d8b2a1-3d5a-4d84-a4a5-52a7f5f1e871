import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { TextInput, <PERSON>ton, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { useAuth } from '../../hooks/useAuth';
import { COLORS, VALIDATION } from '../../constants';

export const ForgotPasswordScreen: React.FC = () => {
  const navigation = useNavigation();
  const { forgotPassword, isLoading } = useAuth();

  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isEmailSent, setIsEmailSent] = useState(false);

  const validateEmail = () => {
    if (!email) {
      setError('Email is required');
      return false;
    } else if (!VALIDATION.EMAIL.PATTERN.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }
    setError('');
    return true;
  };

  const handleResetPassword = async () => {
    if (!validateEmail()) return;

    const result = await forgotPassword(email);
    if (result.success) {
      setIsEmailSent(true);
    }
  };

  const updateEmail = (value: string) => {
    setEmail(value);
    if (error) setError('');
  };

  if (isEmailSent) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Check Your Email</Text>
            <Text style={styles.subtitle}>
              We've sent a password reset link to {email}
            </Text>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <Button
                mode="contained"
                onPress={() => navigation.navigate('Login' as never)}
                style={styles.button}
              >
                Back to Login
              </Button>

              <Button
                mode="text"
                onPress={() => {
                  setIsEmailSent(false);
                  setEmail('');
                }}
                style={styles.backButton}
              >
                Try Different Email
              </Button>
            </Card.Content>
          </Card>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Reset Password</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you a link to reset your password
          </Text>
        </View>

        <Card style={styles.card}>
          <Card.Content>
            <TextInput
              label="Email"
              value={email}
              onChangeText={updateEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!error}
              style={styles.input}
            />
            {error && <Text style={styles.errorText}>{error}</Text>}

            <Button
              mode="contained"
              onPress={handleResetPassword}
              loading={isLoading}
              disabled={!email || isLoading}
              style={styles.button}
            >
              Send Reset Link
            </Button>

            <Button
              mode="text"
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            >
              Back to Login
            </Button>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.GRAY[50],
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.GRAY[600],
    textAlign: 'center',
    lineHeight: 22,
  },
  card: {
    marginBottom: 24,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 12,
    marginBottom: 16,
    marginLeft: 12,
  },
  button: {
    marginTop: 8,
    paddingVertical: 8,
  },
  backButton: {
    marginTop: 8,
  },
});

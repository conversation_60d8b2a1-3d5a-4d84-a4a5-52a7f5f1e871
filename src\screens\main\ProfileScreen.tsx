import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Card, Button, List, Divider } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useAuthStore } from '../../store/authStore';
import { COLORS } from '../../constants';

export const ProfileScreen: React.FC = () => {
  const { user, logout } = useAuthStore();

  const handleLogout = () => {
    logout();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.title}>Profile</Text>
        
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.name}>{user?.displayName}</Text>
            <Text style={styles.email}>{user?.email}</Text>
            <Text style={styles.userType}>
              {user?.userType === 'PREMIUM' ? 'Premium User' : 'Free User'}
            </Text>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <List.Item
            title="Account Settings"
            description="Manage your account information"
            left={(props) => <List.Icon {...props} icon="account-cog" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {/* TODO: Navigate to account settings */}}
          />
          <Divider />
          <List.Item
            title="KYC Verification"
            description={`Status: ${user?.kycStatus || 'Not Started'}`}
            left={(props) => <List.Icon {...props} icon="shield-check" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {/* TODO: Navigate to KYC */}}
          />
          <Divider />
          <List.Item
            title="Wallet"
            description="Manage your XI Coins"
            left={(props) => <List.Icon {...props} icon="wallet" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {/* TODO: Navigate to wallet */}}
          />
          <Divider />
          <List.Item
            title="Transaction History"
            description="View your transaction history"
            left={(props) => <List.Icon {...props} icon="history" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {/* TODO: Navigate to transactions */}}
          />
          <Divider />
          <List.Item
            title="Help & Support"
            description="Get help and contact support"
            left={(props) => <List.Icon {...props} icon="help-circle" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {/* TODO: Navigate to help */}}
          />
        </Card>

        <Button
          mode="outlined"
          onPress={handleLogout}
          style={styles.logoutButton}
          textColor={COLORS.ERROR}
        >
          Logout
        </Button>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.GRAY[50],
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 24,
  },
  card: {
    marginBottom: 16,
  },
  name: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.GRAY[900],
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: COLORS.GRAY[600],
    marginBottom: 8,
  },
  userType: {
    fontSize: 14,
    color: COLORS.ACCENT,
    fontWeight: '600',
  },
  logoutButton: {
    marginTop: 24,
    borderColor: COLORS.ERROR,
  },
});

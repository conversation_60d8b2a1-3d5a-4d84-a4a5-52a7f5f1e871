import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getFirestore, Firestore } from 'firebase/firestore';
import { getStorage, FirebaseStorage } from 'firebase/storage';
import { getFunctions, Functions } from 'firebase/functions';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY || "your-api-key",
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN || "first-xi-app.firebaseapp.com",
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || "first-xi-app",
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET || "first-xi-app.appspot.com",
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID || "1:123456789:web:abcdef123456",
  measurementId: process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID || "G-ABCDEF123456"
};

// Initialize Firebase
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// Initialize Firebase services
export const auth: Auth = getAuth(app);
export const db: Firestore = getFirestore(app);
export const storage: FirebaseStorage = getStorage(app);
export const functions: Functions = getFunctions(app);

// Set Firebase Functions region (for better performance in Nigeria)
// functions.region = 'europe-west1'; // Closest to Nigeria

export default app;

// Firebase configuration validation
export const validateFirebaseConfig = (): boolean => {
  const requiredKeys = [
    'apiKey',
    'authDomain', 
    'projectId',
    'storageBucket',
    'messagingSenderId',
    'appId'
  ];

  return requiredKeys.every(key => 
    firebaseConfig[key as keyof typeof firebaseConfig] && 
    firebaseConfig[key as keyof typeof firebaseConfig] !== `your-${key.toLowerCase().replace(/([A-Z])/g, '-$1')}`
  );
};

// Database collections
export const COLLECTIONS = {
  USERS: 'users',
  WALLETS: 'wallets',
  TRANSACTIONS: 'transactions',
  PLAYERS: 'players',
  CLUBS: 'clubs',
  LEAGUES: 'leagues',
  USER_TEAMS: 'userTeams',
  GAMEWEEKS: 'gameweeks',
  FIXTURES: 'fixtures',
  CONTESTS: 'contests',
  CONTEST_PARTICIPANTS: 'contestParticipants',
  LEADERBOARDS: 'leaderboards',
  KYC_DOCUMENTS: 'kycDocuments',
  PLAYER_STATS: 'playerStats',
  TEAM_HISTORY: 'teamHistory',
  NOTIFICATIONS: 'notifications',
  ADMIN_LOGS: 'adminLogs'
} as const;

// Firestore indexes that should be created
export const REQUIRED_INDEXES = [
  // Users
  { collection: 'users', fields: ['email'], unique: true },
  { collection: 'users', fields: ['phone'], unique: true },
  { collection: 'users', fields: ['kycStatus', 'userType'] },
  
  // Wallets
  { collection: 'wallets', fields: ['userId'], unique: true },
  
  // Transactions
  { collection: 'transactions', fields: ['walletId', 'createdAt'] },
  { collection: 'transactions', fields: ['userId', 'type', 'status'] },
  { collection: 'transactions', fields: ['status', 'createdAt'] },
  
  // Players
  { collection: 'players', fields: ['position', 'club.id'] },
  { collection: 'players', fields: ['price', 'totalPoints'] },
  { collection: 'players', fields: ['club.league.id', 'position'] },
  
  // User Teams
  { collection: 'userTeams', fields: ['userId', 'gameweek'] },
  { collection: 'userTeams', fields: ['gameweek', 'totalValue'] },
  
  // Gameweeks
  { collection: 'gameweeks', fields: ['isActive', 'isCurrent'] },
  { collection: 'gameweeks', fields: ['season', 'number'] },
  
  // Contests
  { collection: 'contests', fields: ['status', 'gameweek'] },
  { collection: 'contests', fields: ['type', 'entryFee'] },
  
  // Contest Participants
  { collection: 'contestParticipants', fields: ['contestId', 'totalPoints'] },
  { collection: 'contestParticipants', fields: ['userId', 'contestId'] },
  
  // Leaderboards
  { collection: 'leaderboards', fields: ['gameweek', 'totalPoints'] },
  { collection: 'leaderboards', fields: ['userId', 'gameweek'] }
];

// Security rules template (to be applied in Firebase Console)
export const SECURITY_RULES_TEMPLATE = `
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read/write their own wallet
    match /wallets/{walletId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Users can read their own transactions
    match /transactions/{transactionId} {
      allow read: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Players and clubs are read-only for users
    match /players/{playerId} {
      allow read: if request.auth != null;
    }
    
    match /clubs/{clubId} {
      allow read: if request.auth != null;
    }
    
    // Users can read/write their own teams
    match /userTeams/{teamId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Gameweeks are read-only
    match /gameweeks/{gameweekId} {
      allow read: if request.auth != null;
    }
    
    // Contests are read-only, participants can be created
    match /contests/{contestId} {
      allow read: if request.auth != null;
    }
    
    match /contestParticipants/{participantId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
  }
}
`;

# First XI Database Setup Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Firebase account
- First XI project cloned

### 1. Firebase Project Setup

1. **Create Firebase Project**
   ```bash
   # Go to https://console.firebase.google.com/
   # Click "Create a project"
   # Name: "first-xi-app" (or your preferred name)
   # Enable Google Analytics (recommended)
   ```

2. **Enable Firestore Database**
   ```bash
   # In Firebase Console:
   # Go to Firestore Database
   # Click "Create database"
   # Choose "Start in test mode" (we'll add security rules later)
   # Select location closest to Nigeria (europe-west1 recommended)
   ```

3. **Get Firebase Configuration**
   ```bash
   # In Firebase Console:
   # Go to Project Settings (gear icon)
   # Scroll to "Your apps" section
   # Click "Web" icon to add web app
   # Register app with name "First XI"
   # Copy the configuration object
   ```

### 2. Environment Configuration

1. **Create Environment File**
   ```bash
   cp .env.example .env
   ```

2. **Update Environment Variables**
   ```bash
   # Edit .env file with your Firebase configuration
   EXPO_PUBLIC_FIREBASE_API_KEY=your-actual-api-key
   EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
   EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
   EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
   EXPO_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456
   ```

### 3. Database Initialization

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Initialize Database**
   ```bash
   # Quick setup with sample data
   npm run db:init
   
   # Or verify configuration only
   npm run db:verify
   ```

3. **Verify Setup**
   ```bash
   # Check if data was seeded correctly
   npm run db:verify
   ```

## 🔧 Detailed Setup

### Firebase Security Rules

1. **Navigate to Firestore Rules**
   ```bash
   # Firebase Console > Firestore Database > Rules
   ```

2. **Apply Security Rules**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       // Users can read/write their own user document
       match /users/{userId} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
       
       // Users can read/write their own wallet
       match /wallets/{walletId} {
         allow read, write: if request.auth != null && 
           resource.data.userId == request.auth.uid;
       }
       
       // Users can read their own transactions
       match /transactions/{transactionId} {
         allow read: if request.auth != null && 
           resource.data.userId == request.auth.uid;
         allow create: if request.auth != null && 
           request.resource.data.userId == request.auth.uid;
       }
       
       // Players and clubs are read-only for users
       match /players/{playerId} {
         allow read: if request.auth != null;
       }
       
       match /clubs/{clubId} {
         allow read: if request.auth != null;
       }
       
       // Users can read/write their own teams
       match /userTeams/{teamId} {
         allow read, write: if request.auth != null && 
           resource.data.userId == request.auth.uid;
       }
       
       // Gameweeks are read-only
       match /gameweeks/{gameweekId} {
         allow read: if request.auth != null;
       }
       
       // Contests are read-only, participants can be created
       match /contests/{contestId} {
         allow read: if request.auth != null;
       }
       
       match /contestParticipants/{participantId} {
         allow read: if request.auth != null;
         allow create: if request.auth != null && 
           request.resource.data.userId == request.auth.uid;
       }
     }
   }
   ```

3. **Test and Publish Rules**
   ```bash
   # Click "Test rules" to validate
   # Click "Publish" to apply
   ```

### Database Indexes

Firebase will automatically suggest indexes as you use the app. For optimal performance, create these indexes manually:

1. **Go to Firestore Indexes**
   ```bash
   # Firebase Console > Firestore Database > Indexes
   ```

2. **Create Composite Indexes**
   ```bash
   # Collection: transactions
   # Fields: userId (Ascending), createdAt (Descending)
   
   # Collection: players  
   # Fields: position (Ascending), totalPoints (Descending)
   
   # Collection: userTeams
   # Fields: userId (Ascending), gameweek (Ascending)
   
   # Collection: contests
   # Fields: status (Ascending), gameweek (Ascending)
   ```

## 🧪 Testing Database Setup

### 1. Connection Test
```bash
npm run db:verify
```

### 2. Data Seeding Test
```bash
# Reset and reseed database
npm run db:reset

# Verify seeded data
npm run db:verify
```

### 3. Manual Testing
```javascript
// Test in browser console or Node.js
import { databaseService } from './src/services/databaseService';

// Test reading players
const players = await databaseService.getPlayers();
console.log('Players:', players);

// Test reading gameweeks
const gameweeks = await databaseService.getGameweeks();
console.log('Gameweeks:', gameweeks);
```

## 🔄 Database Commands

### Development Commands
```bash
# Initialize database with sample data
npm run db:init

# Reset database (clear + reseed)
npm run db:reset

# Verify database configuration
npm run db:verify
```

### Production Commands
```bash
# Production setup (no sample data)
npm run db:production
```

### Manual Operations
```javascript
// Import services
import { seedService } from './src/services/seedService';
import { databaseService } from './src/services/databaseService';

// Clear all data
await seedService.clearAll();

// Seed specific data
await seedService.seedLeagues();
await seedService.seedEPLClubs();
await seedService.seedSamplePlayers();
await seedService.seedGameweeks();

// Verify seeding
await seedService.verifySeeding();
```

## 🚨 Troubleshooting

### Common Issues

1. **Firebase Configuration Error**
   ```bash
   Error: Firebase configuration is invalid
   
   Solution:
   - Check .env file exists and has correct values
   - Verify Firebase project settings
   - Ensure all required environment variables are set
   ```

2. **Permission Denied**
   ```bash
   Error: Missing or insufficient permissions
   
   Solution:
   - Check Firebase security rules are published
   - Verify user authentication
   - Ensure user has correct permissions
   ```

3. **Index Missing**
   ```bash
   Error: The query requires an index
   
   Solution:
   - Click the provided link to create index
   - Or manually create index in Firebase Console
   - Wait for index to build (can take several minutes)
   ```

4. **Network Error**
   ```bash
   Error: Failed to connect to Firebase
   
   Solution:
   - Check internet connection
   - Verify Firebase project is active
   - Check firewall/proxy settings
   ```

### Debug Mode
```bash
# Enable debug logging
EXPO_PUBLIC_DEBUG_MODE=true npm run db:init
```

### Reset Everything
```bash
# Complete reset (use with caution)
npm run db:reset
```

## 📊 Monitoring

### Firebase Console Monitoring
- **Usage**: Monitor read/write operations
- **Performance**: Check query performance
- **Errors**: Review error logs
- **Billing**: Monitor costs

### Application Monitoring
```javascript
// Add to your app for monitoring
import { db } from './src/config/firebase';
import { enableNetwork, disableNetwork } from 'firebase/firestore';

// Monitor connection status
const monitorConnection = () => {
  // Implementation for connection monitoring
};
```

## 🔐 Security Best Practices

1. **Environment Variables**
   - Never commit .env to version control
   - Use different Firebase projects for dev/prod
   - Rotate API keys regularly

2. **Security Rules**
   - Test rules thoroughly
   - Use least privilege principle
   - Regular security audits

3. **Data Validation**
   - Validate data on client and server
   - Use TypeScript for type safety
   - Implement proper error handling

## 📈 Performance Optimization

1. **Query Optimization**
   - Use indexes for all queries
   - Limit query results
   - Implement pagination

2. **Data Structure**
   - Denormalize for read performance
   - Keep documents under 1MB
   - Use subcollections for large datasets

3. **Caching**
   - Enable offline persistence
   - Cache frequently accessed data
   - Use real-time listeners sparingly

## 🚀 Next Steps

After successful database setup:

1. ✅ **Test Authentication Integration**
2. ✅ **Implement User Registration Flow**
3. ⏳ **Set up External API Integration**
4. ⏳ **Configure Payment Processing**
5. ⏳ **Implement Real-time Features**

## 📚 Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Best Practices](https://firebase.google.com/docs/firestore/best-practices)
- [Security Rules Guide](https://firebase.google.com/docs/firestore/security/get-started)
- [Performance Monitoring](https://firebase.google.com/docs/perf-mon)

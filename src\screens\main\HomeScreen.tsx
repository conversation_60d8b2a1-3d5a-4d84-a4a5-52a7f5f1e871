import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Card, Button, Chip } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useAuthStore } from '../../store/authStore';
import { COLORS } from '../../constants';

export const HomeScreen: React.FC = () => {
  const { user } = useAuthStore();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text style={styles.greeting}>Welcome back, {user?.displayName}!</Text>
          <Chip icon="trophy" style={styles.chip}>
            Gameweek 1
          </Chip>
        </View>

        <Card style={styles.card}>
          <Card.Title title="Your Team" subtitle="First XI Squad" />
          <Card.Content>
            <Text style={styles.cardText}>
              You haven't created your team yet. Build your squad to start playing!
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button mode="contained">Build Squad</Button>
          </Card.Actions>
        </Card>

        <Card style={styles.card}>
          <Card.Title title="XI Coins Wallet" subtitle="Your Balance" />
          <Card.Content>
            <Text style={styles.balance}>₦0.00</Text>
            <Text style={styles.cardText}>
              Add funds to your wallet to start staking on players
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button mode="outlined">Add Funds</Button>
          </Card.Actions>
        </Card>

        <Card style={styles.card}>
          <Card.Title title="This Gameweek" subtitle="Upcoming Fixtures" />
          <Card.Content>
            <Text style={styles.cardText}>
              No fixtures available. Check back later for upcoming matches.
            </Text>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Title title="Leaderboard" subtitle="Your Ranking" />
          <Card.Content>
            <Text style={styles.cardText}>
              Create your team to see your ranking on the leaderboard
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button mode="text">View Leaderboard</Button>
          </Card.Actions>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.GRAY[50],
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  greeting: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    flex: 1,
  },
  chip: {
    backgroundColor: COLORS.ACCENT,
  },
  card: {
    marginBottom: 16,
  },
  cardText: {
    color: COLORS.GRAY[600],
    lineHeight: 20,
  },
  balance: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.SUCCESS,
    marginBottom: 8,
  },
});

// App Configuration
export const APP_CONFIG = {
  NAME: 'First XI',
  VERSION: '1.0.0',
  CURRENCY: {
    PRIMARY: 'NGN',
    COIN_NAME: 'XI Coins',
    EXCHANGE_RATE: 1, // 1 NGN = 1 XI Coin
  },
  TEAM_LIMITS: {
    TOTAL_PLAYERS: 15,
    ACTIVE_PLAYERS: 11,
    SUBSTITUTES: 4,
    MAX_PLAYERS_PER_CLUB: 3,
    BUDGET: 100, // XI Coins
  },
  SCORING: {
    CAPTAIN_MULTIPLIER: 2,
    VICE_CAPTAIN_MULTIPLIER: 1.5,
  },
} as const;

// Scoring Rules
export const SCORING_RULES = {
  GOAL: {
    GK: 6,
    DEF: 6,
    MID: 5,
    FWD: 4,
  },
  ASSIST: 3,
  CLEAN_SHEET: {
    GK: 4,
    DEF: 4,
    MID: 1,
    FWD: 0,
  },
  YELLOW_CARD: -1,
  RED_CARD: -3,
  OWN_GOAL: -2,
  PENALTY_MISS: -2,
  PENALTY_SAVE: 5,
  SAVE: {
    EVERY_3_SAVES: 1,
  },
  MINUTES_PLAYED: {
    THRESHOLD: 60,
    POINTS: 2,
    UNDER_THRESHOLD: 1,
  },
} as const;

// Player Positions
export const POSITIONS = {
  GK: 'Goalkeeper',
  DEF: 'Defender',
  MID: 'Midfielder',
  FWD: 'Forward',
} as const;

export const POSITION_LIMITS = {
  GK: { min: 1, max: 1 },
  DEF: { min: 3, max: 5 },
  MID: { min: 3, max: 5 },
  FWD: { min: 1, max: 3 },
} as const;

// Formations
export const FORMATIONS = [
  '3-4-3',
  '3-5-2',
  '4-3-3',
  '4-4-2',
  '4-5-1',
  '5-3-2',
  '5-4-1',
] as const;

export const FORMATION_POSITIONS = {
  '3-4-3': { GK: 1, DEF: 3, MID: 4, FWD: 3 },
  '3-5-2': { GK: 1, DEF: 3, MID: 5, FWD: 2 },
  '4-3-3': { GK: 1, DEF: 4, MID: 3, FWD: 3 },
  '4-4-2': { GK: 1, DEF: 4, MID: 4, FWD: 2 },
  '4-5-1': { GK: 1, DEF: 4, MID: 5, FWD: 1 },
  '5-3-2': { GK: 1, DEF: 5, MID: 3, FWD: 2 },
  '5-4-1': { GK: 1, DEF: 5, MID: 4, FWD: 1 },
} as const;

// Leagues
export const SUPPORTED_LEAGUES = {
  EPL: {
    id: 'epl',
    name: 'English Premier League',
    country: 'England',
    apiId: 39, // API Football ID
  },
  LA_LIGA: {
    id: 'la_liga',
    name: 'La Liga',
    country: 'Spain',
    apiId: 140,
  },
  NPFL: {
    id: 'npfl',
    name: 'Nigeria Professional Football League',
    country: 'Nigeria',
    apiId: 387,
  },
} as const;

// Colors
export const COLORS = {
  PRIMARY: '#1E3A8A', // Blue
  SECONDARY: '#10B981', // Green
  ACCENT: '#F59E0B', // Amber
  ERROR: '#EF4444', // Red
  WARNING: '#F59E0B', // Amber
  SUCCESS: '#10B981', // Green
  INFO: '#3B82F6', // Blue
  
  // Neutral Colors
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
  
  // Position Colors
  POSITION: {
    GK: '#F59E0B', // Amber
    DEF: '#3B82F6', // Blue
    MID: '#10B981', // Green
    FWD: '#EF4444', // Red
  },
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  BASE_URL: __DEV__ ? 'http://localhost:3000/api' : 'https://api.firstxi.ng/api',
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    VERIFY_OTP: '/auth/verify-otp',
    FORGOT_PASSWORD: '/auth/forgot-password',
  },
  USER: {
    PROFILE: '/user/profile',
    UPDATE_PROFILE: '/user/profile',
    KYC: '/user/kyc',
    UPLOAD_DOCUMENT: '/user/kyc/upload',
  },
  WALLET: {
    BALANCE: '/wallet/balance',
    TRANSACTIONS: '/wallet/transactions',
    DEPOSIT: '/wallet/deposit',
    WITHDRAW: '/wallet/withdraw',
  },
  PLAYERS: {
    LIST: '/players',
    DETAILS: '/players/:id',
    SEARCH: '/players/search',
  },
  TEAMS: {
    MY_TEAM: '/teams/my-team',
    CREATE: '/teams',
    UPDATE: '/teams/:id',
    TRANSFERS: '/teams/transfers',
  },
  GAMEWEEKS: {
    CURRENT: '/gameweeks/current',
    LIST: '/gameweeks',
    DETAILS: '/gameweeks/:id',
  },
  CONTESTS: {
    LIST: '/contests',
    JOIN: '/contests/:id/join',
    LEAVE: '/contests/:id/leave',
    LEADERBOARD: '/contests/:id/leaderboard',
  },
  LEADERBOARD: {
    GLOBAL: '/leaderboard/global',
    GAMEWEEK: '/leaderboard/gameweek/:id',
  },
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: '@first_xi_auth_token',
  REFRESH_TOKEN: '@first_xi_refresh_token',
  USER_DATA: '@first_xi_user_data',
  THEME: '@first_xi_theme',
  ONBOARDING_COMPLETED: '@first_xi_onboarding_completed',
  LANGUAGE: '@first_xi_language',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'Session expired. Please login again.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'Resource not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Welcome back!',
  REGISTER_SUCCESS: 'Account created successfully!',
  TEAM_SAVED: 'Team saved successfully!',
  TRANSFER_COMPLETED: 'Transfer completed successfully!',
  DEPOSIT_SUCCESS: 'Deposit completed successfully!',
  WITHDRAWAL_SUCCESS: 'Withdrawal request submitted successfully!',
} as const;

// Validation Rules
export const VALIDATION = {
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  },
  PHONE: {
    PATTERN: /^(\+234|0)[789][01]\d{8}$/, // Nigerian phone number pattern
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  NIN: {
    PATTERN: /^\d{11}$/, // Nigerian National Identification Number
  },
} as const;

// Feature Flags
export const FEATURES = {
  KYC_REQUIRED: true,
  MULTIPLE_LEAGUES: true,
  HEAD_TO_HEAD: true,
  CONTESTS: true,
  SOCIAL_LOGIN: true,
  PUSH_NOTIFICATIONS: true,
  DARK_MODE: true,
} as const;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>First XI Authentication Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007AFF;
            background: #f8f9fa;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        input.error {
            border-color: #dc3545;
        }
        input.success {
            border-color: #28a745;
        }
        .error-text {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        .success-text {
            color: #28a745;
            font-size: 14px;
            margin-top: 5px;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 First XI Authentication System Test</h1>
        
        <div class="test-section">
            <h2>📋 Test Results</h2>
            <div id="test-results"></div>
            <button onclick="runAllTests()">Run All Tests</button>
        </div>

        <div class="test-section">
            <h2>🔐 Interactive Login Test</h2>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" placeholder="Enter email address" oninput="validateField('email')">
                <div id="email-error" class="error-text"></div>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter password" oninput="validateField('password')">
                <div id="password-error" class="error-text"></div>
            </div>
            <button onclick="testLogin()">Test Login</button>
        </div>

        <div class="test-section">
            <h2>📝 Interactive Registration Test</h2>
            <div class="form-group">
                <label for="reg-name">Full Name:</label>
                <input type="text" id="reg-name" placeholder="Enter full name" oninput="validateField('reg-name')">
                <div id="reg-name-error" class="error-text"></div>
            </div>
            <div class="form-group">
                <label for="reg-email">Email:</label>
                <input type="email" id="reg-email" placeholder="Enter email address" oninput="validateField('reg-email')">
                <div id="reg-email-error" class="error-text"></div>
            </div>
            <div class="form-group">
                <label for="reg-phone">Phone (Optional):</label>
                <input type="tel" id="reg-phone" placeholder="+*************" oninput="validateField('reg-phone')">
                <div id="reg-phone-error" class="error-text"></div>
            </div>
            <div class="form-group">
                <label for="reg-password">Password:</label>
                <input type="password" id="reg-password" placeholder="Enter password" oninput="validateField('reg-password')">
                <div id="reg-password-error" class="error-text"></div>
            </div>
            <div class="form-group">
                <label for="reg-confirm">Confirm Password:</label>
                <input type="password" id="reg-confirm" placeholder="Confirm password" oninput="validateField('reg-confirm')">
                <div id="reg-confirm-error" class="error-text"></div>
            </div>
            <button onclick="testRegistration()">Test Registration</button>
        </div>
    </div>

    <script>
        // Validation patterns (same as our React Native app)
        const VALIDATION = {
            EMAIL: {
                PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                MIN_LENGTH: 5,
                MAX_LENGTH: 254,
            },
            PASSWORD: {
                MIN_LENGTH: 8,
                MAX_LENGTH: 128,
                PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
            },
            PHONE: {
                PATTERN: /^\+?[1-9]\d{1,14}$/,
            },
            NAME: {
                MIN_LENGTH: 2,
                MAX_LENGTH: 50,
                PATTERN: /^[a-zA-Z\s'-]+$/,
            },
        };

        // Validation functions (same logic as our React Native app)
        function validateEmail(email) {
            if (!email) return 'Email is required';
            if (!VALIDATION.EMAIL.PATTERN.test(email)) return 'Please enter a valid email address';
            if (email.length < VALIDATION.EMAIL.MIN_LENGTH) return 'Email is too short';
            if (email.length > VALIDATION.EMAIL.MAX_LENGTH) return 'Email is too long';
            return null;
        }

        function validatePassword(password) {
            if (!password) return 'Password is required';
            if (password.length < VALIDATION.PASSWORD.MIN_LENGTH) return 'Password must be at least 8 characters';
            if (password.length > VALIDATION.PASSWORD.MAX_LENGTH) return 'Password is too long';
            if (!VALIDATION.PASSWORD.PATTERN.test(password)) {
                return 'Password must contain uppercase, lowercase, number and special character';
            }
            return null;
        }

        function validateName(name) {
            if (!name) return 'Name is required';
            if (name.length < VALIDATION.NAME.MIN_LENGTH) return 'Name is too short';
            if (name.length > VALIDATION.NAME.MAX_LENGTH) return 'Name is too long';
            if (!VALIDATION.NAME.PATTERN.test(name)) return 'Name contains invalid characters';
            return null;
        }

        function validatePhone(phone) {
            if (!phone) return null; // Phone is optional
            if (!VALIDATION.PHONE.PATTERN.test(phone)) return 'Please enter a valid phone number';
            return null;
        }

        function validateField(fieldId) {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + '-error');
            let error = null;

            switch(fieldId) {
                case 'email':
                case 'reg-email':
                    error = validateEmail(field.value);
                    break;
                case 'password':
                case 'reg-password':
                    error = validatePassword(field.value);
                    break;
                case 'reg-name':
                    error = validateName(field.value);
                    break;
                case 'reg-phone':
                    error = validatePhone(field.value);
                    break;
                case 'reg-confirm':
                    const password = document.getElementById('reg-password').value;
                    if (field.value !== password) {
                        error = 'Passwords do not match';
                    }
                    break;
            }

            if (error) {
                field.className = 'error';
                errorDiv.textContent = error;
                errorDiv.className = 'error-text';
            } else {
                field.className = field.value ? 'success' : '';
                errorDiv.textContent = field.value ? '✓ Valid' : '';
                errorDiv.className = field.value ? 'success-text' : '';
            }

            return !error;
        }

        function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const emailValid = validateField('email');
            const passwordValid = validateField('password');
            
            if (emailValid && passwordValid) {
                alert('✅ Login validation passed! In the real app, this would authenticate with the backend.');
            } else {
                alert('❌ Please fix the validation errors before proceeding.');
            }
        }

        function testRegistration() {
            const fields = ['reg-name', 'reg-email', 'reg-phone', 'reg-password', 'reg-confirm'];
            const results = fields.map(field => validateField(field));
            
            if (results.every(result => result)) {
                alert('✅ Registration validation passed! In the real app, this would create a new account.');
            } else {
                alert('❌ Please fix the validation errors before proceeding.');
            }
        }

        function runAllTests() {
            const testCases = [
                { type: 'email', input: '<EMAIL>', expected: null },
                { type: 'email', input: 'invalid-email', expected: 'Please enter a valid email address' },
                { type: 'password', input: 'Password123!', expected: null },
                { type: 'password', input: 'weak', expected: 'Password must be at least 8 characters' },
                { type: 'name', input: 'John Doe', expected: null },
                { type: 'name', input: 'A', expected: 'Name is too short' },
                { type: 'phone', input: '+*************', expected: null },
                { type: 'phone', input: 'invalid', expected: 'Please enter a valid phone number' },
            ];

            let results = '';
            let passed = 0;
            let failed = 0;

            testCases.forEach((testCase, index) => {
                let result;
                switch (testCase.type) {
                    case 'email': result = validateEmail(testCase.input); break;
                    case 'password': result = validatePassword(testCase.input); break;
                    case 'name': result = validateName(testCase.input); break;
                    case 'phone': result = validatePhone(testCase.input); break;
                }

                const success = result === testCase.expected;
                if (success) {
                    passed++;
                    results += `<div class="test-result test-pass">✅ Test ${index + 1}: ${testCase.type} validation - PASSED</div>`;
                } else {
                    failed++;
                    results += `<div class="test-result test-fail">❌ Test ${index + 1}: ${testCase.type} validation - FAILED<br>Expected: ${testCase.expected}<br>Got: ${result}</div>`;
                }
            });

            results += `<div class="test-result ${failed === 0 ? 'test-pass' : 'test-fail'}">📊 Results: ${passed} passed, ${failed} failed</div>`;
            document.getElementById('test-results').innerHTML = results;
        }

        // Run tests on page load
        window.onload = function() {
            runAllTests();
        };
    </script>
</body>
</html>

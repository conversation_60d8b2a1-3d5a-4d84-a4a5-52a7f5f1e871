import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BaseToast, ErrorToast, InfoToast, ToastConfig } from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';

import { COLORS } from '../constants';

const toastConfig: ToastConfig = {
  success: (props) => (
    <BaseToast
      {...props}
      style={[styles.baseToast, styles.successToast]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="checkmark-circle" size={24} color={COLORS.SUCCESS} />
        </View>
      )}
    />
  ),
  
  error: (props) => (
    <ErrorToast
      {...props}
      style={[styles.baseToast, styles.errorToast]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="close-circle" size={24} color={COLORS.ERROR} />
        </View>
      )}
    />
  ),
  
  info: (props) => (
    <InfoToast
      {...props}
      style={[styles.baseToast, styles.infoToast]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="information-circle" size={24} color={COLORS.INFO} />
        </View>
      )}
    />
  ),
  
  warning: (props) => (
    <BaseToast
      {...props}
      style={[styles.baseToast, styles.warningToast]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="warning" size={24} color={COLORS.WARNING} />
        </View>
      )}
    />
  ),
};

const styles = StyleSheet.create({
  baseToast: {
    borderLeftWidth: 0,
    borderRadius: 12,
    marginHorizontal: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  
  successToast: {
    backgroundColor: COLORS.WHITE,
    borderLeftColor: COLORS.SUCCESS,
    borderLeftWidth: 4,
  },
  
  errorToast: {
    backgroundColor: COLORS.WHITE,
    borderLeftColor: COLORS.ERROR,
    borderLeftWidth: 4,
  },
  
  infoToast: {
    backgroundColor: COLORS.WHITE,
    borderLeftColor: COLORS.INFO,
    borderLeftWidth: 4,
  },
  
  warningToast: {
    backgroundColor: COLORS.WHITE,
    borderLeftColor: COLORS.WARNING,
    borderLeftWidth: 4,
  },
  
  contentContainer: {
    paddingHorizontal: 0,
    paddingVertical: 0,
    flex: 1,
  },
  
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    width: 32,
    height: 32,
  },
  
  text1: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.GRAY[900],
    marginBottom: 2,
  },
  
  text2: {
    fontSize: 14,
    fontWeight: '400',
    color: COLORS.GRAY[600],
    lineHeight: 18,
  },
});

export default toastConfig;

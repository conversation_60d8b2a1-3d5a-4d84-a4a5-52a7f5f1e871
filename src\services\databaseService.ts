import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot,
  QueryConstraint,
  writeBatch,
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove,
  Timestamp
} from 'firebase/firestore';
import { db, COLLECTIONS } from '../config/firebase';
import { 
  User, 
  Wallet, 
  Transaction, 
  Player, 
  Club, 
  League, 
  UserTeam, 
  Gameweek, 
  Contest, 
  ContestParticipant,
  LeaderboardEntry,
  KYCDocument,
  ApiResponse 
} from '../types';

export class DatabaseService {
  // Generic CRUD operations
  async create<T>(collectionName: string, id: string, data: Omit<T, 'id'>): Promise<ApiResponse<T>> {
    try {
      const docRef = doc(db, collectionName, id);
      const docData = {
        ...data,
        id,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };
      
      await setDoc(docRef, docData);
      
      return { 
        success: true, 
        data: { ...docData, id } as T 
      };
    } catch (error) {
      console.error(`Error creating document in ${collectionName}:`, error);
      return { 
        success: false, 
        error: `Failed to create ${collectionName.slice(0, -1)}` 
      };
    }
  }

  async read<T>(collectionName: string, id: string): Promise<ApiResponse<T>> {
    try {
      const docRef = doc(db, collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { 
          success: true, 
          data: { id: docSnap.id, ...docSnap.data() } as T 
        };
      } else {
        return { 
          success: false, 
          error: `${collectionName.slice(0, -1)} not found` 
        };
      }
    } catch (error) {
      console.error(`Error reading document from ${collectionName}:`, error);
      return { 
        success: false, 
        error: `Failed to read ${collectionName.slice(0, -1)}` 
      };
    }
  }

  async update<T>(collectionName: string, id: string, data: Partial<T>): Promise<ApiResponse<T>> {
    try {
      const docRef = doc(db, collectionName, id);
      const updateData = {
        ...data,
        updatedAt: serverTimestamp()
      };
      
      await updateDoc(docRef, updateData);
      
      // Get updated document
      const updatedDoc = await this.read<T>(collectionName, id);
      return updatedDoc;
    } catch (error) {
      console.error(`Error updating document in ${collectionName}:`, error);
      return { 
        success: false, 
        error: `Failed to update ${collectionName.slice(0, -1)}` 
      };
    }
  }

  async delete(collectionName: string, id: string): Promise<ApiResponse<void>> {
    try {
      const docRef = doc(db, collectionName, id);
      await deleteDoc(docRef);
      
      return { success: true };
    } catch (error) {
      console.error(`Error deleting document from ${collectionName}:`, error);
      return { 
        success: false, 
        error: `Failed to delete ${collectionName.slice(0, -1)}` 
      };
    }
  }

  async query<T>(
    collectionName: string, 
    constraints: QueryConstraint[] = [],
    limitCount?: number
  ): Promise<ApiResponse<T[]>> {
    try {
      const collectionRef = collection(db, collectionName);
      const queryConstraints = limitCount ? [...constraints, limit(limitCount)] : constraints;
      const q = query(collectionRef, ...queryConstraints);
      
      const querySnapshot = await getDocs(q);
      const results: T[] = [];
      
      querySnapshot.forEach((doc) => {
        results.push({ id: doc.id, ...doc.data() } as T);
      });
      
      return { success: true, data: results };
    } catch (error) {
      console.error(`Error querying ${collectionName}:`, error);
      return { 
        success: false, 
        error: `Failed to query ${collectionName}` 
      };
    }
  }

  // User-specific operations
  async createUser(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<User>> {
    return this.create<User>(COLLECTIONS.USERS, user.email, user);
  }

  async getUserByEmail(email: string): Promise<ApiResponse<User>> {
    const result = await this.query<User>(
      COLLECTIONS.USERS,
      [where('email', '==', email)],
      1
    );
    
    if (result.success && result.data && result.data.length > 0) {
      return { success: true, data: result.data[0] };
    }
    
    return { success: false, error: 'User not found' };
  }

  async getUserByPhone(phone: string): Promise<ApiResponse<User>> {
    const result = await this.query<User>(
      COLLECTIONS.USERS,
      [where('phone', '==', phone)],
      1
    );
    
    if (result.success && result.data && result.data.length > 0) {
      return { success: true, data: result.data[0] };
    }
    
    return { success: false, error: 'User not found' };
  }

  // Wallet operations
  async createWallet(wallet: Omit<Wallet, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Wallet>> {
    return this.create<Wallet>(COLLECTIONS.WALLETS, wallet.userId, wallet);
  }

  async getUserWallet(userId: string): Promise<ApiResponse<Wallet>> {
    return this.read<Wallet>(COLLECTIONS.WALLETS, userId);
  }

  async updateWalletBalance(userId: string, amount: number): Promise<ApiResponse<Wallet>> {
    try {
      const walletRef = doc(db, COLLECTIONS.WALLETS, userId);
      await updateDoc(walletRef, {
        balance: increment(amount),
        updatedAt: serverTimestamp()
      });
      
      return this.getUserWallet(userId);
    } catch (error) {
      console.error('Error updating wallet balance:', error);
      return { success: false, error: 'Failed to update wallet balance' };
    }
  }

  // Transaction operations
  async createTransaction(transaction: Omit<Transaction, 'id' | 'createdAt'>): Promise<ApiResponse<Transaction>> {
    const transactionId = `${transaction.walletId}_${Date.now()}`;
    return this.create<Transaction>(COLLECTIONS.TRANSACTIONS, transactionId, transaction);
  }

  async getUserTransactions(userId: string, limitCount: number = 50): Promise<ApiResponse<Transaction[]>> {
    return this.query<Transaction>(
      COLLECTIONS.TRANSACTIONS,
      [
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      ],
      limitCount
    );
  }

  // Player operations
  async getPlayers(position?: string, clubId?: string): Promise<ApiResponse<Player[]>> {
    const constraints: QueryConstraint[] = [];
    
    if (position) {
      constraints.push(where('position', '==', position));
    }
    
    if (clubId) {
      constraints.push(where('club.id', '==', clubId));
    }
    
    constraints.push(orderBy('totalPoints', 'desc'));
    
    return this.query<Player>(COLLECTIONS.PLAYERS, constraints);
  }

  async searchPlayers(searchTerm: string): Promise<ApiResponse<Player[]>> {
    // Note: Firestore doesn't support full-text search natively
    // This is a basic implementation - consider using Algolia for production
    return this.query<Player>(
      COLLECTIONS.PLAYERS,
      [
        where('name', '>=', searchTerm),
        where('name', '<=', searchTerm + '\uf8ff'),
        orderBy('name'),
        orderBy('totalPoints', 'desc')
      ],
      20
    );
  }

  // Team operations
  async createUserTeam(team: Omit<UserTeam, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<UserTeam>> {
    const teamId = `${team.userId}_gw${team.gameweek}`;
    return this.create<UserTeam>(COLLECTIONS.USER_TEAMS, teamId, team);
  }

  async getUserTeam(userId: string, gameweek: number): Promise<ApiResponse<UserTeam>> {
    const teamId = `${userId}_gw${gameweek}`;
    return this.read<UserTeam>(COLLECTIONS.USER_TEAMS, teamId);
  }

  async updateUserTeam(userId: string, gameweek: number, updates: Partial<UserTeam>): Promise<ApiResponse<UserTeam>> {
    const teamId = `${userId}_gw${gameweek}`;
    return this.update<UserTeam>(COLLECTIONS.USER_TEAMS, teamId, updates);
  }

  // Gameweek operations
  async getCurrentGameweek(): Promise<ApiResponse<Gameweek>> {
    const result = await this.query<Gameweek>(
      COLLECTIONS.GAMEWEEKS,
      [where('isCurrent', '==', true)],
      1
    );
    
    if (result.success && result.data && result.data.length > 0) {
      return { success: true, data: result.data[0] };
    }
    
    return { success: false, error: 'No current gameweek found' };
  }

  async getGameweeks(season?: string): Promise<ApiResponse<Gameweek[]>> {
    const constraints: QueryConstraint[] = [orderBy('number', 'asc')];
    
    if (season) {
      constraints.unshift(where('season', '==', season));
    }
    
    return this.query<Gameweek>(COLLECTIONS.GAMEWEEKS, constraints);
  }

  // Contest operations
  async getActiveContests(gameweek: number): Promise<ApiResponse<Contest[]>> {
    return this.query<Contest>(
      COLLECTIONS.CONTESTS,
      [
        where('gameweek', '==', gameweek),
        where('status', 'in', ['OPEN', 'LIVE']),
        orderBy('entryFee', 'asc')
      ]
    );
  }

  async joinContest(contestId: string, participant: ContestParticipant): Promise<ApiResponse<void>> {
    try {
      const batch = writeBatch(db);
      
      // Add participant
      const participantRef = doc(db, COLLECTIONS.CONTEST_PARTICIPANTS, `${contestId}_${participant.userId}`);
      batch.set(participantRef, {
        ...participant,
        contestId,
        createdAt: serverTimestamp()
      });
      
      // Update contest participant count
      const contestRef = doc(db, COLLECTIONS.CONTESTS, contestId);
      batch.update(contestRef, {
        currentParticipants: increment(1),
        updatedAt: serverTimestamp()
      });
      
      await batch.commit();
      return { success: true };
    } catch (error) {
      console.error('Error joining contest:', error);
      return { success: false, error: 'Failed to join contest' };
    }
  }

  // Leaderboard operations
  async getLeaderboard(gameweek?: number, limitCount: number = 100): Promise<ApiResponse<LeaderboardEntry[]>> {
    const constraints: QueryConstraint[] = [
      orderBy('totalPoints', 'desc'),
      orderBy('teamValue', 'desc')
    ];
    
    if (gameweek) {
      constraints.unshift(where('gameweek', '==', gameweek));
    }
    
    return this.query<LeaderboardEntry>(COLLECTIONS.LEADERBOARDS, constraints, limitCount);
  }

  // Batch operations for data seeding
  async batchCreate<T>(collectionName: string, items: Array<T & { id: string }>): Promise<ApiResponse<void>> {
    try {
      const batch = writeBatch(db);
      
      items.forEach((item) => {
        const docRef = doc(db, collectionName, item.id);
        batch.set(docRef, {
          ...item,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      });
      
      await batch.commit();
      return { success: true };
    } catch (error) {
      console.error(`Error batch creating ${collectionName}:`, error);
      return { success: false, error: `Failed to batch create ${collectionName}` };
    }
  }
}

export const databaseService = new DatabaseService();

// Wallet Service Extension
export class WalletService extends DatabaseService {
  async processTransaction(
    userId: string,
    type: 'DEPOSIT' | 'WITHDRAWAL' | 'STAKE' | 'WINNINGS' | 'REFUND',
    amount: number,
    description: string,
    reference?: string
  ): Promise<ApiResponse<Transaction>> {
    try {
      // Get user wallet
      const walletResult = await this.getUserWallet(userId);
      if (!walletResult.success) {
        return { success: false, error: 'Wallet not found' };
      }

      const wallet = walletResult.data!;

      // Validate transaction
      if (type === 'WITHDRAWAL' || type === 'STAKE') {
        if (wallet.balance < amount) {
          return { success: false, error: 'Insufficient balance' };
        }
      }

      // Create transaction
      const transaction: Omit<Transaction, 'id' | 'createdAt'> = {
        walletId: wallet.id,
        userId,
        type,
        amount: type === 'WITHDRAWAL' || type === 'STAKE' ? -amount : amount,
        currency: 'XI_COINS',
        status: type === 'WITHDRAWAL' ? 'PENDING' : 'COMPLETED',
        description,
        reference
      };

      const transactionResult = await this.createTransaction(transaction);
      if (!transactionResult.success) {
        return transactionResult;
      }

      // Update wallet balance if transaction is completed
      if (transaction.status === 'COMPLETED') {
        await this.updateWalletBalance(userId, transaction.amount);
      }

      return transactionResult;
    } catch (error) {
      console.error('Error processing transaction:', error);
      return { success: false, error: 'Failed to process transaction' };
    }
  }

  async getWalletSummary(userId: string): Promise<ApiResponse<{
    balance: number;
    totalDeposits: number;
    totalWithdrawals: number;
    totalStakes: number;
    totalWinnings: number;
    recentTransactions: Transaction[];
  }>> {
    try {
      const walletResult = await this.getUserWallet(userId);
      const transactionsResult = await this.getUserTransactions(userId, 10);

      if (!walletResult.success || !transactionsResult.success) {
        return { success: false, error: 'Failed to get wallet summary' };
      }

      const wallet = walletResult.data!;
      const allTransactionsResult = await this.getUserTransactions(userId, 1000);
      const allTransactions = allTransactionsResult.success ? allTransactionsResult.data! : [];

      const summary = {
        balance: wallet.balance,
        totalDeposits: allTransactions
          .filter(t => t.type === 'DEPOSIT' && t.status === 'COMPLETED')
          .reduce((sum, t) => sum + t.amount, 0),
        totalWithdrawals: Math.abs(allTransactions
          .filter(t => t.type === 'WITHDRAWAL' && t.status === 'COMPLETED')
          .reduce((sum, t) => sum + t.amount, 0)),
        totalStakes: Math.abs(allTransactions
          .filter(t => t.type === 'STAKE' && t.status === 'COMPLETED')
          .reduce((sum, t) => sum + t.amount, 0)),
        totalWinnings: allTransactions
          .filter(t => t.type === 'WINNINGS' && t.status === 'COMPLETED')
          .reduce((sum, t) => sum + t.amount, 0),
        recentTransactions: transactionsResult.data!
      };

      return { success: true, data: summary };
    } catch (error) {
      console.error('Error getting wallet summary:', error);
      return { success: false, error: 'Failed to get wallet summary' };
    }
  }
}

export const walletService = new WalletService();

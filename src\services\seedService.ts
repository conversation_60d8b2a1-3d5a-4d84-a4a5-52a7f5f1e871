import { databaseService } from './databaseService';
import { COLLECTIONS } from '../config/firebase';
import { SUPPORTED_LEAGUES, SCORING_RULES } from '../constants';
import { 
  League, 
  Club, 
  Player, 
  Gameweek, 
  PlayerPosition,
  ApiResponse 
} from '../types';

export class SeedService {
  // Seed leagues
  async seedLeagues(): Promise<ApiResponse<void>> {
    console.log('🌱 Seeding leagues...');
    
    const leagues: League[] = Object.values(SUPPORTED_LEAGUES).map(league => ({
      id: league.id,
      name: league.name,
      country: league.country,
      season: '2024-25',
      isActive: true
    }));

    return databaseService.batchCreate(COLLECTIONS.LEAGUES, leagues);
  }

  // Seed EPL clubs
  async seedEPLClubs(): Promise<ApiResponse<void>> {
    console.log('🌱 Seeding EPL clubs...');
    
    const eplClubs: Club[] = [
      { id: 'arsenal', name: 'Arsenal', shortName: 'ARS', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'aston-villa', name: 'Aston Villa', shortName: 'AVL', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'bournemouth', name: 'AFC Bournemouth', shortName: 'BOU', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'brentford', name: 'Brentford', shortName: 'BRE', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'brighton', name: 'Brighton & Hove Albion', shortName: 'BHA', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'chelsea', name: 'Chelsea', shortName: 'CHE', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'crystal-palace', name: 'Crystal Palace', shortName: 'CRY', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'everton', name: 'Everton', shortName: 'EVE', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'fulham', name: 'Fulham', shortName: 'FUL', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'ipswich', name: 'Ipswich Town', shortName: 'IPS', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'leicester', name: 'Leicester City', shortName: 'LEI', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'liverpool', name: 'Liverpool', shortName: 'LIV', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'man-city', name: 'Manchester City', shortName: 'MCI', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'man-united', name: 'Manchester United', shortName: 'MUN', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'newcastle', name: 'Newcastle United', shortName: 'NEW', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'nottingham-forest', name: 'Nottingham Forest', shortName: 'NFO', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'southampton', name: 'Southampton', shortName: 'SOU', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'tottenham', name: 'Tottenham Hotspur', shortName: 'TOT', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'west-ham', name: 'West Ham United', shortName: 'WHU', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
      { id: 'wolves', name: 'Wolverhampton Wanderers', shortName: 'WOL', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] }
    ];

    return databaseService.batchCreate(COLLECTIONS.CLUBS, eplClubs);
  }

  // Seed sample players for testing
  async seedSamplePlayers(): Promise<ApiResponse<void>> {
    console.log('🌱 Seeding sample players...');
    
    const samplePlayers: Player[] = [
      // Arsenal players
      {
        id: 'bukayo-saka',
        name: 'Bukayo Saka',
        position: 'MID' as PlayerPosition,
        club: { id: 'arsenal', name: 'Arsenal', shortName: 'ARS', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
        nationality: 'England',
        age: 22,
        price: 10.5,
        totalPoints: 145,
        form: [8, 6, 9, 7, 8],
        isInjured: false,
        isSuspended: false,
        stats: {
          goals: 8,
          assists: 12,
          cleanSheets: 5,
          yellowCards: 2,
          redCards: 0,
          minutesPlayed: 2340
        }
      },
      {
        id: 'martin-odegaard',
        name: 'Martin Ødegaard',
        position: 'MID' as PlayerPosition,
        club: { id: 'arsenal', name: 'Arsenal', shortName: 'ARS', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
        nationality: 'Norway',
        age: 25,
        price: 8.5,
        totalPoints: 132,
        form: [7, 8, 6, 9, 7],
        isInjured: false,
        isSuspended: false,
        stats: {
          goals: 6,
          assists: 10,
          cleanSheets: 4,
          yellowCards: 3,
          redCards: 0,
          minutesPlayed: 2180
        }
      },
      // Manchester City players
      {
        id: 'erling-haaland',
        name: 'Erling Haaland',
        position: 'FWD' as PlayerPosition,
        club: { id: 'man-city', name: 'Manchester City', shortName: 'MCI', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
        nationality: 'Norway',
        age: 24,
        price: 15.0,
        totalPoints: 198,
        form: [12, 8, 10, 9, 11],
        isInjured: false,
        isSuspended: false,
        stats: {
          goals: 22,
          assists: 5,
          cleanSheets: 0,
          yellowCards: 1,
          redCards: 0,
          minutesPlayed: 2520
        }
      },
      {
        id: 'kevin-de-bruyne',
        name: 'Kevin De Bruyne',
        position: 'MID' as PlayerPosition,
        club: { id: 'man-city', name: 'Manchester City', shortName: 'MCI', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
        nationality: 'Belgium',
        age: 33,
        price: 9.5,
        totalPoints: 156,
        form: [9, 7, 8, 10, 6],
        isInjured: false,
        isSuspended: false,
        stats: {
          goals: 7,
          assists: 15,
          cleanSheets: 6,
          yellowCards: 2,
          redCards: 0,
          minutesPlayed: 2280
        }
      },
      // Liverpool players
      {
        id: 'mohamed-salah',
        name: 'Mohamed Salah',
        position: 'FWD' as PlayerPosition,
        club: { id: 'liverpool', name: 'Liverpool', shortName: 'LIV', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
        nationality: 'Egypt',
        age: 32,
        price: 13.0,
        totalPoints: 187,
        form: [10, 9, 8, 11, 9],
        isInjured: false,
        isSuspended: false,
        stats: {
          goals: 19,
          assists: 8,
          cleanSheets: 0,
          yellowCards: 1,
          redCards: 0,
          minutesPlayed: 2640
        }
      },
      // Goalkeepers
      {
        id: 'alisson-becker',
        name: 'Alisson Becker',
        position: 'GK' as PlayerPosition,
        club: { id: 'liverpool', name: 'Liverpool', shortName: 'LIV', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
        nationality: 'Brazil',
        age: 31,
        price: 5.5,
        totalPoints: 142,
        form: [6, 8, 7, 9, 6],
        isInjured: false,
        isSuspended: false,
        stats: {
          goals: 0,
          assists: 1,
          cleanSheets: 12,
          yellowCards: 0,
          redCards: 0,
          minutesPlayed: 2700,
          saves: 89,
          penaltiesSaved: 2
        }
      },
      // Defenders
      {
        id: 'virgil-van-dijk',
        name: 'Virgil van Dijk',
        position: 'DEF' as PlayerPosition,
        club: { id: 'liverpool', name: 'Liverpool', shortName: 'LIV', league: { id: 'epl', name: 'English Premier League', country: 'England', season: '2024-25', isActive: true }, players: [] },
        nationality: 'Netherlands',
        age: 33,
        price: 6.5,
        totalPoints: 134,
        form: [6, 7, 8, 6, 7],
        isInjured: false,
        isSuspended: false,
        stats: {
          goals: 3,
          assists: 2,
          cleanSheets: 12,
          yellowCards: 2,
          redCards: 0,
          minutesPlayed: 2610
        }
      }
    ];

    return databaseService.batchCreate(COLLECTIONS.PLAYERS, samplePlayers);
  }

  // Seed gameweeks for current season
  async seedGameweeks(): Promise<ApiResponse<void>> {
    console.log('🌱 Seeding gameweeks...');
    
    const gameweeks: Gameweek[] = [];
    const season = '2024-25';
    const startDate = new Date('2024-08-17'); // EPL season start
    
    for (let i = 1; i <= 38; i++) {
      const gwStartDate = new Date(startDate);
      gwStartDate.setDate(startDate.getDate() + (i - 1) * 7);
      
      const gwEndDate = new Date(gwStartDate);
      gwEndDate.setDate(gwStartDate.getDate() + 6);
      
      const deadlineDate = new Date(gwStartDate);
      deadlineDate.setHours(11, 30, 0, 0); // 11:30 AM deadline
      
      const now = new Date();
      const isActive = now >= gwStartDate && now <= gwEndDate;
      const isCurrent = i === 1; // Set first gameweek as current for testing
      
      gameweeks.push({
        id: `gw${i}_${season}`,
        number: i,
        season,
        startDate: gwStartDate,
        endDate: gwEndDate,
        deadlineDate,
        isActive,
        isCurrent,
        fixtures: [] // Will be populated by external API
      });
    }

    return databaseService.batchCreate(COLLECTIONS.GAMEWEEKS, gameweeks);
  }

  // Seed all data
  async seedAll(): Promise<ApiResponse<void>> {
    console.log('🌱 Starting database seeding...');
    
    try {
      // Seed in order due to dependencies
      await this.seedLeagues();
      console.log('✅ Leagues seeded');
      
      await this.seedEPLClubs();
      console.log('✅ EPL clubs seeded');
      
      await this.seedSamplePlayers();
      console.log('✅ Sample players seeded');
      
      await this.seedGameweeks();
      console.log('✅ Gameweeks seeded');
      
      console.log('🎉 Database seeding completed successfully!');
      return { success: true };
    } catch (error) {
      console.error('❌ Database seeding failed:', error);
      return { success: false, error: 'Database seeding failed' };
    }
  }

  // Clear all data (for testing)
  async clearAll(): Promise<ApiResponse<void>> {
    console.log('🧹 Clearing all data...');
    
    try {
      const collections = [
        COLLECTIONS.PLAYERS,
        COLLECTIONS.CLUBS,
        COLLECTIONS.LEAGUES,
        COLLECTIONS.GAMEWEEKS,
        COLLECTIONS.USER_TEAMS,
        COLLECTIONS.CONTESTS,
        COLLECTIONS.CONTEST_PARTICIPANTS,
        COLLECTIONS.LEADERBOARDS
      ];

      for (const collectionName of collections) {
        const result = await databaseService.query(collectionName);
        if (result.success && result.data) {
          for (const doc of result.data) {
            await databaseService.delete(collectionName, doc.id);
          }
        }
      }
      
      console.log('✅ All data cleared');
      return { success: true };
    } catch (error) {
      console.error('❌ Failed to clear data:', error);
      return { success: false, error: 'Failed to clear data' };
    }
  }

  // Verify seeded data
  async verifySeeding(): Promise<ApiResponse<{ [key: string]: number }>> {
    console.log('🔍 Verifying seeded data...');
    
    try {
      const counts: { [key: string]: number } = {};
      
      const collections = [
        COLLECTIONS.LEAGUES,
        COLLECTIONS.CLUBS,
        COLLECTIONS.PLAYERS,
        COLLECTIONS.GAMEWEEKS
      ];

      for (const collectionName of collections) {
        const result = await databaseService.query(collectionName);
        counts[collectionName] = result.success && result.data ? result.data.length : 0;
      }
      
      console.log('📊 Seeding verification:', counts);
      return { success: true, data: counts };
    } catch (error) {
      console.error('❌ Failed to verify seeding:', error);
      return { success: false, error: 'Failed to verify seeding' };
    }
  }
}

export const seedService = new SeedService();

import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, ApiResponse } from '../types';
import { STORAGE_KEYS, API_ENDPOINTS } from '../constants';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  displayName: string;
  email: string;
  phone?: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface OTPRequest {
  phone: string;
}

export interface OTPVerification {
  phone: string;
  otp: string;
}

class AuthService {
  private baseUrl = API_ENDPOINTS.BASE_URL;

  // Email/Password Authentication
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.AUTH.LOGIN}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (response.ok) {
        // Store tokens
        await this.storeTokens(data.data.token, data.data.refreshToken);
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  }

  async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.AUTH.REGISTER}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (response.ok) {
        await this.storeTokens(data.data.token, data.data.refreshToken);
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message || 'Registration failed' };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  }

  // Phone OTP Authentication
  async sendOTP(otpRequest: OTPRequest): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/send-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(otpRequest),
      });

      const data = await response.json();

      if (response.ok) {
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message || 'Failed to send OTP' };
      }
    } catch (error) {
      console.error('Send OTP error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  }

  async verifyOTP(verification: OTPVerification): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.AUTH.VERIFY_OTP}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(verification),
      });

      const data = await response.json();

      if (response.ok) {
        await this.storeTokens(data.data.token, data.data.refreshToken);
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message || 'OTP verification failed' };
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  }

  // Password Reset
  async forgotPassword(email: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.AUTH.FORGOT_PASSWORD}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message || 'Failed to send reset email' };
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  }

  // Token Management
  async storeTokens(token: string, refreshToken: string): Promise<void> {
    try {
      await AsyncStorage.multiSet([
        [STORAGE_KEYS.AUTH_TOKEN, token],
        [STORAGE_KEYS.REFRESH_TOKEN, refreshToken],
      ]);
    } catch (error) {
      console.error('Error storing tokens:', error);
    }
  }

  async getStoredToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  async getStoredRefreshToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    } catch (error) {
      console.error('Error getting stored refresh token:', error);
      return null;
    }
  }

  async refreshToken(): Promise<ApiResponse<{ token: string; refreshToken: string }>> {
    try {
      const refreshToken = await this.getStoredRefreshToken();
      if (!refreshToken) {
        return { success: false, error: 'No refresh token available' };
      }

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.AUTH.REFRESH}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${refreshToken}`,
        },
      });

      const data = await response.json();

      if (response.ok) {
        await this.storeTokens(data.data.token, data.data.refreshToken);
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message || 'Token refresh failed' };
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  }

  async logout(): Promise<void> {
    try {
      const token = await this.getStoredToken();
      if (token) {
        // Call logout endpoint
        await fetch(`${this.baseUrl}${API_ENDPOINTS.AUTH.LOGOUT}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear stored tokens regardless of API call success
      await this.clearTokens();
    }
  }

  async clearTokens(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
        STORAGE_KEYS.USER_DATA,
      ]);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }

  // Social Authentication (placeholder methods)
  async loginWithGoogle(): Promise<ApiResponse<AuthResponse>> {
    // TODO: Implement Google authentication
    return { success: false, error: 'Google authentication not implemented yet' };
  }

  async loginWithApple(): Promise<ApiResponse<AuthResponse>> {
    // TODO: Implement Apple authentication
    return { success: false, error: 'Apple authentication not implemented yet' };
  }

  async loginWithFacebook(): Promise<ApiResponse<AuthResponse>> {
    // TODO: Implement Facebook authentication
    return { success: false, error: 'Facebook authentication not implemented yet' };
  }

  async loginWithTwitter(): Promise<ApiResponse<AuthResponse>> {
    // TODO: Implement Twitter authentication
    return { success: false, error: 'Twitter authentication not implemented yet' };
  }
}

export const authService = new AuthService();

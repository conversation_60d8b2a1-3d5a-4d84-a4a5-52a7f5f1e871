import { DefaultTheme } from 'react-native-paper';
import { COLORS } from './index';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: COLORS.PRIMARY,
    accent: COLORS.ACCENT,
    background: COLORS.WHITE,
    surface: COLORS.WHITE,
    text: COLORS.BLACK,
    error: COLORS.ERROR,
    success: COLORS.SUCCESS,
    warning: COLORS.WARNING,
    info: COLORS.INFO,
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: 'System',
      fontWeight: '400' as const,
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100' as const,
    },
  },
};

export const darkTheme = {
  ...theme,
  colors: {
    ...theme.colors,
    background: COLORS.GRAY[900],
    surface: COLORS.GRAY[800],
    text: COLORS.WHITE,
  },
};

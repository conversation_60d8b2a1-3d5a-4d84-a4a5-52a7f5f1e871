{"license": "0BSD", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~52.0.47", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.9", "@expo/vector-icons": "~14.0.4", "react-native-paper": "4.9.2"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}
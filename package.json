{"name": "first-xi", "version": "1.0.0", "description": "Fantasy Football Staking Application for Nigerian Market", "license": "0BSD", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"expo": "~52.0.47", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.9", "@expo/vector-icons": "~14.0.4", "react-native-paper": "4.9.2", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "~3.29.0", "react-native-safe-area-context": "4.8.2", "expo-auth-session": "~5.5.2", "expo-crypto": "~13.0.2", "expo-web-browser": "~13.0.3", "firebase": "^10.7.1", "@react-native-async-storage/async-storage": "1.21.0", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "expo-linear-gradient": "~13.0.2", "react-native-svg": "14.1.0", "expo-font": "~12.0.9", "expo-splash-screen": "~0.27.6", "react-hook-form": "^7.48.2", "zustand": "^4.4.7", "date-fns": "^2.30.0", "react-native-toast-message": "^2.1.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.70.6", "typescript": "^5.1.3"}, "private": true}
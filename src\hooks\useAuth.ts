import { useState, useCallback } from 'react';
import Toast from 'react-native-toast-message';

import { useAuthStore } from '../store/authStore';
import { authService, LoginCredentials, RegisterData, OTPRequest, OTPVerification } from '../services/authService';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '../constants';

export const useAuth = () => {
  const { user, setUser, setTokens, logout: logoutStore, setLoading } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);

  const login = useCallback(async (credentials: LoginCredentials) => {
    setIsLoading(true);
    setLoading(true);

    try {
      const result = await authService.login(credentials);
      
      if (result.success && result.data) {
        setUser(result.data.user);
        setTokens(result.data.token, result.data.refreshToken);
        
        Toast.show({
          type: 'success',
          text1: SUCCESS_MESSAGES.LOGIN_SUCCESS,
          text2: `Welcome back, ${result.data.user.displayName}!`,
        });
        
        return { success: true };
      } else {
        Toast.show({
          type: 'error',
          text1: 'Login Failed',
          text2: result.error || ERROR_MESSAGES.UNKNOWN_ERROR,
        });
        
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Login hook error:', error);
      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: ERROR_MESSAGES.NETWORK_ERROR,
      });
      
      return { success: false, error: ERROR_MESSAGES.NETWORK_ERROR };
    } finally {
      setIsLoading(false);
      setLoading(false);
    }
  }, [setUser, setTokens, setLoading]);

  const register = useCallback(async (userData: RegisterData) => {
    setIsLoading(true);
    setLoading(true);

    try {
      const result = await authService.register(userData);
      
      if (result.success && result.data) {
        setUser(result.data.user);
        setTokens(result.data.token, result.data.refreshToken);
        
        Toast.show({
          type: 'success',
          text1: SUCCESS_MESSAGES.REGISTER_SUCCESS,
          text2: `Welcome to First XI, ${result.data.user.displayName}!`,
        });
        
        return { success: true };
      } else {
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2: result.error || ERROR_MESSAGES.UNKNOWN_ERROR,
        });
        
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Registration hook error:', error);
      Toast.show({
        type: 'error',
        text1: 'Registration Failed',
        text2: ERROR_MESSAGES.NETWORK_ERROR,
      });
      
      return { success: false, error: ERROR_MESSAGES.NETWORK_ERROR };
    } finally {
      setIsLoading(false);
      setLoading(false);
    }
  }, [setUser, setTokens, setLoading]);

  const sendOTP = useCallback(async (otpRequest: OTPRequest) => {
    setIsLoading(true);

    try {
      const result = await authService.sendOTP(otpRequest);
      
      if (result.success) {
        Toast.show({
          type: 'success',
          text1: 'OTP Sent',
          text2: `Verification code sent to ${otpRequest.phone}`,
        });
        
        return { success: true };
      } else {
        Toast.show({
          type: 'error',
          text1: 'Failed to Send OTP',
          text2: result.error || ERROR_MESSAGES.UNKNOWN_ERROR,
        });
        
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Send OTP hook error:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to Send OTP',
        text2: ERROR_MESSAGES.NETWORK_ERROR,
      });
      
      return { success: false, error: ERROR_MESSAGES.NETWORK_ERROR };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const verifyOTP = useCallback(async (verification: OTPVerification) => {
    setIsLoading(true);
    setLoading(true);

    try {
      const result = await authService.verifyOTP(verification);
      
      if (result.success && result.data) {
        setUser(result.data.user);
        setTokens(result.data.token, result.data.refreshToken);
        
        Toast.show({
          type: 'success',
          text1: 'Phone Verified',
          text2: 'Your phone number has been verified successfully!',
        });
        
        return { success: true };
      } else {
        Toast.show({
          type: 'error',
          text1: 'Verification Failed',
          text2: result.error || ERROR_MESSAGES.UNKNOWN_ERROR,
        });
        
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('OTP verification hook error:', error);
      Toast.show({
        type: 'error',
        text1: 'Verification Failed',
        text2: ERROR_MESSAGES.NETWORK_ERROR,
      });
      
      return { success: false, error: ERROR_MESSAGES.NETWORK_ERROR };
    } finally {
      setIsLoading(false);
      setLoading(false);
    }
  }, [setUser, setTokens, setLoading]);

  const forgotPassword = useCallback(async (email: string) => {
    setIsLoading(true);

    try {
      const result = await authService.forgotPassword(email);
      
      if (result.success) {
        Toast.show({
          type: 'success',
          text1: 'Reset Link Sent',
          text2: `Password reset link sent to ${email}`,
        });
        
        return { success: true };
      } else {
        Toast.show({
          type: 'error',
          text1: 'Failed to Send Reset Link',
          text2: result.error || ERROR_MESSAGES.UNKNOWN_ERROR,
        });
        
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Forgot password hook error:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to Send Reset Link',
        text2: ERROR_MESSAGES.NETWORK_ERROR,
      });
      
      return { success: false, error: ERROR_MESSAGES.NETWORK_ERROR };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    setIsLoading(true);
    setLoading(true);

    try {
      await authService.logout();
      logoutStore();
      
      Toast.show({
        type: 'success',
        text1: 'Logged Out',
        text2: 'You have been logged out successfully',
      });
    } catch (error) {
      console.error('Logout hook error:', error);
      // Still logout locally even if API call fails
      logoutStore();
    } finally {
      setIsLoading(false);
      setLoading(false);
    }
  }, [logoutStore, setLoading]);

  // Social authentication methods
  const loginWithGoogle = useCallback(async () => {
    setIsLoading(true);
    setLoading(true);

    try {
      const result = await authService.loginWithGoogle();
      
      if (result.success && result.data) {
        setUser(result.data.user);
        setTokens(result.data.token, result.data.refreshToken);
        
        Toast.show({
          type: 'success',
          text1: 'Login Successful',
          text2: `Welcome, ${result.data.user.displayName}!`,
        });
        
        return { success: true };
      } else {
        Toast.show({
          type: 'error',
          text1: 'Google Login Failed',
          text2: result.error || ERROR_MESSAGES.UNKNOWN_ERROR,
        });
        
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Google login hook error:', error);
      Toast.show({
        type: 'error',
        text1: 'Google Login Failed',
        text2: ERROR_MESSAGES.NETWORK_ERROR,
      });
      
      return { success: false, error: ERROR_MESSAGES.NETWORK_ERROR };
    } finally {
      setIsLoading(false);
      setLoading(false);
    }
  }, [setUser, setTokens, setLoading]);

  return {
    user,
    isLoading,
    login,
    register,
    sendOTP,
    verifyOTP,
    forgotPassword,
    logout,
    loginWithGoogle,
    // TODO: Add other social login methods
    loginWithApple: () => Promise.resolve({ success: false, error: 'Not implemented' }),
    loginWithFacebook: () => Promise.resolve({ success: false, error: 'Not implemented' }),
    loginWithTwitter: () => Promise.resolve({ success: false, error: 'Not implemented' }),
  };
};

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { COLORS } from '../../constants';

export const SquadScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>My Squad</Text>
        
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.emptyText}>
              You haven't created your squad yet.
            </Text>
            <Text style={styles.description}>
              Build your team with 11 active players and 4 substitutes to start playing.
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button mode="contained" style={styles.button}>
              Create Squad
            </Button>
          </Card.Actions>
        </Card>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.GRAY[50],
  },
  content: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 24,
  },
  card: {
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.GRAY[700],
    marginBottom: 8,
  },
  description: {
    color: COLORS.GRAY[600],
    lineHeight: 20,
    marginBottom: 16,
  },
  button: {
    flex: 1,
  },
});

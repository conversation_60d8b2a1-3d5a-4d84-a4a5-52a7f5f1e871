import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useAuthStore } from '../store/authStore';
import { COLORS } from '../constants';

export const OnboardingScreen: React.FC = () => {
  const { completeOnboarding } = useAuthStore();

  const handleCompleteOnboarding = () => {
    completeOnboarding();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Welcome to First XI</Text>
        <Text style={styles.subtitle}>
          Build your fantasy football team and compete with friends!
        </Text>
        <Text style={styles.description}>
          • Create your dream team with 15 players{'\n'}
          • Stake XI Coins on your players{'\n'}
          • Compete in weekly leaderboards{'\n'}
          • Win prizes based on real player performance
        </Text>
        <Button
          mode="contained"
          onPress={handleCompleteOnboarding}
          style={styles.button}
        >
          Get Started
        </Button>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: COLORS.GRAY[600],
    marginBottom: 32,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: COLORS.GRAY[700],
    marginBottom: 48,
    textAlign: 'left',
    lineHeight: 24,
  },
  button: {
    width: '100%',
    paddingVertical: 8,
  },
});

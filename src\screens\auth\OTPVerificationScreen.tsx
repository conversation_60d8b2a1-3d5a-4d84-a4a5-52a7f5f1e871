import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { TextInput, Button, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { RouteProp, useRoute } from '@react-navigation/native';

import { AuthStackParamList } from '../../types';
import { COLORS } from '../../constants';

type OTPVerificationRouteProp = RouteProp<AuthStackParamList, 'OTPVerification'>;

export const OTPVerificationScreen: React.FC = () => {
  const route = useRoute<OTPVerificationRouteProp>();
  const { phone } = route.params;
  
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleVerifyOTP = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement OTP verification logic
      console.log('Verify OTP:', otp, 'for phone:', phone);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.error('OTP verification error:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Verify Phone Number</Text>
          <Text style={styles.subtitle}>
            Enter the 6-digit code sent to {phone}
          </Text>
        </View>

        <Card style={styles.card}>
          <Card.Content>
            <TextInput
              label="Verification Code"
              value={otp}
              onChangeText={setOtp}
              mode="outlined"
              keyboardType="numeric"
              maxLength={6}
              style={styles.input}
            />

            <Button
              mode="contained"
              onPress={handleVerifyOTP}
              loading={isLoading}
              disabled={otp.length !== 6}
              style={styles.button}
            >
              Verify
            </Button>

            <Button
              mode="text"
              onPress={() => {/* TODO: Resend OTP */}}
              style={styles.resendButton}
            >
              Resend Code
            </Button>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.GRAY[50],
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.GRAY[600],
    textAlign: 'center',
    lineHeight: 22,
  },
  card: {
    marginBottom: 24,
  },
  input: {
    marginBottom: 16,
    textAlign: 'center',
  },
  button: {
    marginTop: 8,
    paddingVertical: 8,
  },
  resendButton: {
    marginTop: 8,
  },
});

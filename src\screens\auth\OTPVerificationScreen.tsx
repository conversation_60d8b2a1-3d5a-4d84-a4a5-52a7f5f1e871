import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { TextInput, <PERSON><PERSON>, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { RouteProp, useRoute } from '@react-navigation/native';

import { useAuth } from '../../hooks/useAuth';
import { AuthStackParamList } from '../../types';
import { COLORS } from '../../constants';

type OTPVerificationRouteProp = RouteProp<AuthStackParamList, 'OTPVerification'>;

export const OTPVerificationScreen: React.FC = () => {
  const route = useRoute<OTPVerificationRouteProp>();
  const { phone } = route.params;
  const { verifyOTP, sendOTP, isLoading } = useAuth();

  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  const validateOTP = () => {
    if (!otp) {
      setError('OTP is required');
      return false;
    } else if (otp.length !== 6) {
      setError('OTP must be 6 digits');
      return false;
    } else if (!/^\d{6}$/.test(otp)) {
      setError('OTP must contain only numbers');
      return false;
    }
    setError('');
    return true;
  };

  const handleVerifyOTP = async () => {
    if (!validateOTP()) return;

    const result = await verifyOTP({ phone, otp });
    if (!result.success) {
      setError(result.error || 'Verification failed');
    }
  };

  const handleResendOTP = async () => {
    const result = await sendOTP({ phone });
    if (result.success) {
      setCountdown(60);
      setCanResend(false);
      setOtp('');
      setError('');
    }
  };

  const updateOTP = (value: string) => {
    // Only allow numbers and limit to 6 digits
    const numericValue = value.replace(/[^0-9]/g, '').slice(0, 6);
    setOtp(numericValue);
    if (error) setError('');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Verify Phone Number</Text>
          <Text style={styles.subtitle}>
            Enter the 6-digit code sent to {phone}
          </Text>
        </View>

        <Card style={styles.card}>
          <Card.Content>
            <TextInput
              label="Verification Code"
              value={otp}
              onChangeText={updateOTP}
              mode="outlined"
              keyboardType="numeric"
              maxLength={6}
              error={!!error}
              style={styles.input}
            />
            {error && <Text style={styles.errorText}>{error}</Text>}

            <Button
              mode="contained"
              onPress={handleVerifyOTP}
              loading={isLoading}
              disabled={otp.length !== 6 || isLoading}
              style={styles.button}
            >
              Verify
            </Button>

            <Button
              mode="text"
              onPress={handleResendOTP}
              disabled={!canResend || isLoading}
              style={styles.resendButton}
            >
              {canResend ? 'Resend Code' : `Resend in ${countdown}s`}
            </Button>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.GRAY[50],
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.GRAY[600],
    textAlign: 'center',
    lineHeight: 22,
  },
  card: {
    marginBottom: 24,
  },
  input: {
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 12,
    marginBottom: 16,
    textAlign: 'center',
  },
  button: {
    marginTop: 8,
    paddingVertical: 8,
  },
  resendButton: {
    marginTop: 8,
  },
});

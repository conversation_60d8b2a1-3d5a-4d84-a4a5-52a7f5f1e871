// User Types
export interface User {
  id: string;
  email: string;
  phone?: string;
  displayName: string;
  avatar?: string;
  userType: 'FREE' | 'PREMIUM';
  kycStatus: 'PENDING' | 'VERIFIED' | 'REJECTED' | 'NOT_STARTED';
  kycDocuments?: KYCDocument[];
  createdAt: Date;
  updatedAt: Date;
}

export interface KYCDocument {
  id: string;
  type: 'NIN' | 'DRIVERS_LICENSE' | 'PASSPORT';
  documentNumber: string;
  documentUrl: string;
  status: 'PENDING' | 'VERIFIED' | 'REJECTED';
  uploadedAt: Date;
}

// Wallet Types
export interface Wallet {
  id: string;
  userId: string;
  balance: number; // XI Coins
  currency: 'NGN' | 'USD' | 'EUR' | 'GBP';
  transactions: Transaction[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Transaction {
  id: string;
  walletId: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'STAKE' | 'WINNINGS' | 'REFUND';
  amount: number;
  currency: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  description: string;
  reference?: string;
  paystackReference?: string;
  createdAt: Date;
  completedAt?: Date;
}

// Player Types
export interface Player {
  id: string;
  name: string;
  position: PlayerPosition;
  club: Club;
  nationality: string;
  age: number;
  photo?: string;
  price: number; // XI Coins
  totalPoints: number;
  form: number[];
  isInjured: boolean;
  isSuspended: boolean;
  nextFixture?: Fixture;
  stats: PlayerStats;
}

export type PlayerPosition = 'GK' | 'DEF' | 'MID' | 'FWD';

export interface PlayerStats {
  goals: number;
  assists: number;
  cleanSheets: number;
  yellowCards: number;
  redCards: number;
  minutesPlayed: number;
  saves?: number; // For goalkeepers
  penaltiesSaved?: number; // For goalkeepers
}

// Club Types
export interface Club {
  id: string;
  name: string;
  shortName: string;
  logo?: string;
  league: League;
  players: Player[];
}

export interface League {
  id: string;
  name: string;
  country: string;
  season: string;
  isActive: boolean;
}

// Team Types
export interface UserTeam {
  id: string;
  userId: string;
  gameweek: number;
  formation: Formation;
  players: TeamPlayer[];
  captain: string; // Player ID
  viceCaptain: string; // Player ID
  totalValue: number;
  isLocked: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TeamPlayer {
  playerId: string;
  position: PlayerPosition;
  isSubstitute: boolean;
  multiplier: number; // 1 for regular, 2 for captain, 1.5 for vice-captain
}

export type Formation = '3-4-3' | '3-5-2' | '4-3-3' | '4-4-2' | '4-5-1' | '5-3-2' | '5-4-1';

// Game Types
export interface Gameweek {
  id: string;
  number: number;
  season: string;
  startDate: Date;
  endDate: Date;
  deadlineDate: Date;
  isActive: boolean;
  isCurrent: boolean;
  fixtures: Fixture[];
}

export interface Fixture {
  id: string;
  gameweek: number;
  homeTeam: Club;
  awayTeam: Club;
  kickoffTime: Date;
  status: 'SCHEDULED' | 'LIVE' | 'FINISHED' | 'POSTPONED';
  homeScore?: number;
  awayScore?: number;
  venue?: string;
}

// Scoring Types
export interface ScoringRule {
  action: ScoringAction;
  points: number;
  position?: PlayerPosition;
}

export type ScoringAction = 
  | 'GOAL' 
  | 'ASSIST' 
  | 'CLEAN_SHEET' 
  | 'YELLOW_CARD' 
  | 'RED_CARD' 
  | 'OWN_GOAL' 
  | 'PENALTY_MISS' 
  | 'PENALTY_SAVE' 
  | 'SAVE' 
  | 'MINUTES_PLAYED';

// Contest Types
export interface Contest {
  id: string;
  name: string;
  type: 'HEAD_TO_HEAD' | 'LEAGUE' | 'CUP';
  entryFee: number;
  maxParticipants: number;
  currentParticipants: number;
  prizePool: number;
  gameweek: number;
  status: 'OPEN' | 'FULL' | 'LIVE' | 'FINISHED';
  participants: ContestParticipant[];
  prizes: Prize[];
  createdAt: Date;
  startDate: Date;
}

export interface ContestParticipant {
  userId: string;
  teamId: string;
  totalPoints: number;
  rank: number;
  entryTime: Date;
}

export interface Prize {
  rank: number;
  amount: number;
  description: string;
}

// Leaderboard Types
export interface LeaderboardEntry {
  userId: string;
  displayName: string;
  avatar?: string;
  totalPoints: number;
  rank: number;
  teamValue: number;
  gameweekPoints: number;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Onboarding: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Squad: undefined;
  Transfers: undefined;
  Leaderboard: undefined;
  Profile: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  OTPVerification: { phone: string };
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

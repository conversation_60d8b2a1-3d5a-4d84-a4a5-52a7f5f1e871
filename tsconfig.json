{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@screens/*": ["./src/screens/*"], "@services/*": ["./src/services/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./src/types/*"], "@store/*": ["./src/store/*"], "@constants/*": ["./src/constants/*"], "@hooks/*": ["./src/hooks/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]}
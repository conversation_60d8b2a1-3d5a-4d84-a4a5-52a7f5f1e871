# First XI Database Schema Documentation

## 🏗️ Architecture Overview

The First XI application uses **Firebase Firestore** as the primary database, providing:
- Real-time data synchronization
- Offline support
- Automatic scaling
- Built-in security rules
- Cross-platform compatibility

## 📊 Database Collections

### 1. Users Collection (`users`)
Stores user account information and preferences.

```typescript
interface User {
  id: string;                    // Document ID (email-based)
  email: string;                 // Unique email address
  phone?: string;                // Optional phone number
  displayName: string;           // User's display name
  avatar?: string;               // Profile picture URL
  userType: 'FREE' | 'PREMIUM';  // Account type
  kycStatus: 'PENDING' | 'VERIFIED' | 'REJECTED' | 'NOT_STARTED';
  kycDocuments?: KYCDocument[];  // KYC verification documents
  createdAt: Date;
  updatedAt: Date;
}
```

**Indexes:**
- `email` (unique)
- `phone` (unique)
- `kycStatus + userType`

### 2. Wallets Collection (`wallets`)
Manages user XI Coins balances and wallet information.

```typescript
interface Wallet {
  id: string;           // Document ID (same as userId)
  userId: string;       // Reference to user
  balance: number;      // XI Coins balance
  currency: string;     // Always 'XI_COINS'
  transactions: Transaction[];
  createdAt: Date;
  updatedAt: Date;
}
```

**Indexes:**
- `userId` (unique)

### 3. Transactions Collection (`transactions`)
Records all financial transactions in the system.

```typescript
interface Transaction {
  id: string;                    // Auto-generated ID
  walletId: string;              // Reference to wallet
  userId: string;                // Reference to user (for queries)
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'STAKE' | 'WINNINGS' | 'REFUND';
  amount: number;                // Transaction amount (negative for debits)
  currency: string;              // Transaction currency
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  description: string;           // Transaction description
  reference?: string;            // External reference
  paystackReference?: string;    // Paystack transaction reference
  createdAt: Date;
  completedAt?: Date;
}
```

**Indexes:**
- `walletId + createdAt`
- `userId + type + status`
- `status + createdAt`

### 4. Players Collection (`players`)
Contains all football player data and statistics.

```typescript
interface Player {
  id: string;                    // Unique player ID
  name: string;                  // Player full name
  position: 'GK' | 'DEF' | 'MID' | 'FWD';
  club: Club;                    // Club information
  nationality: string;           // Player nationality
  age: number;                   // Player age
  photo?: string;                // Player photo URL
  price: number;                 // XI Coins price
  totalPoints: number;           // Season total points
  form: number[];                // Last 5 gameweek points
  isInjured: boolean;            // Injury status
  isSuspended: boolean;          // Suspension status
  nextFixture?: Fixture;         // Next match
  stats: PlayerStats;            // Detailed statistics
}
```

**Indexes:**
- `position + club.id`
- `price + totalPoints`
- `club.league.id + position`

### 5. Clubs Collection (`clubs`)
Football club information and metadata.

```typescript
interface Club {
  id: string;           // Unique club ID
  name: string;         // Full club name
  shortName: string;    // 3-letter abbreviation
  logo?: string;        // Club logo URL
  league: League;       // League information
  players: Player[];    // Club players
}
```

### 6. User Teams Collection (`userTeams`)
User's fantasy team selections for each gameweek.

```typescript
interface UserTeam {
  id: string;                    // Format: {userId}_gw{gameweek}
  userId: string;                // Team owner
  gameweek: number;              // Gameweek number
  formation: Formation;          // Team formation
  players: TeamPlayer[];         // Selected players
  captain: string;               // Captain player ID
  viceCaptain: string;           // Vice-captain player ID
  totalValue: number;            // Team total value
  isLocked: boolean;             // Transfer lock status
  createdAt: Date;
  updatedAt: Date;
}
```

**Indexes:**
- `userId + gameweek`
- `gameweek + totalValue`

### 7. Gameweeks Collection (`gameweeks`)
Game week information and scheduling.

```typescript
interface Gameweek {
  id: string;           // Format: gw{number}_{season}
  number: number;       // Gameweek number (1-38)
  season: string;       // Season (e.g., "2024-25")
  startDate: Date;      // Gameweek start
  endDate: Date;        // Gameweek end
  deadlineDate: Date;   // Transfer deadline
  isActive: boolean;    // Currently active
  isCurrent: boolean;   // Current gameweek
  fixtures: Fixture[];  // Gameweek fixtures
}
```

**Indexes:**
- `isActive + isCurrent`
- `season + number`

### 8. Contests Collection (`contests`)
Competition and contest information.

```typescript
interface Contest {
  id: string;                    // Auto-generated ID
  name: string;                  // Contest name
  type: 'HEAD_TO_HEAD' | 'LEAGUE' | 'CUP';
  entryFee: number;              // Entry fee in XI Coins
  maxParticipants: number;       // Maximum participants
  currentParticipants: number;   // Current participant count
  prizePool: number;             // Total prize pool
  gameweek: number;              // Contest gameweek
  status: 'OPEN' | 'FULL' | 'LIVE' | 'FINISHED';
  participants: ContestParticipant[];
  prizes: Prize[];               // Prize distribution
  createdAt: Date;
  startDate: Date;
}
```

**Indexes:**
- `status + gameweek`
- `type + entryFee`

### 9. Contest Participants Collection (`contestParticipants`)
Tracks user participation in contests.

```typescript
interface ContestParticipant {
  id: string;           // Format: {contestId}_{userId}
  contestId: string;    // Contest reference
  userId: string;       // Participant user ID
  teamId: string;       // User team reference
  totalPoints: number;  // Contest points
  rank: number;         // Current rank
  entryTime: Date;      // Entry timestamp
}
```

**Indexes:**
- `contestId + totalPoints`
- `userId + contestId`

### 10. Leaderboards Collection (`leaderboards`)
Global and gameweek leaderboard entries.

```typescript
interface LeaderboardEntry {
  id: string;           // Format: {userId}_{gameweek}
  userId: string;       // User reference
  displayName: string;  // User display name
  avatar?: string;      // User avatar
  totalPoints: number;  // Total season points
  rank: number;         // Current rank
  teamValue: number;    // Team value
  gameweekPoints: number; // Gameweek points
  gameweek?: number;    // Specific gameweek (optional)
}
```

**Indexes:**
- `gameweek + totalPoints`
- `userId + gameweek`

## 🔐 Security Rules

Firebase security rules ensure data protection:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Wallet access restricted to owner
    match /wallets/{walletId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Public read access for players and clubs
    match /players/{playerId} {
      allow read: if request.auth != null;
    }
    
    // User teams restricted to owner
    match /userTeams/{teamId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
  }
}
```

## 📈 Performance Optimization

### Indexing Strategy
- **Composite indexes** for complex queries
- **Single-field indexes** for simple lookups
- **Array-contains indexes** for array queries

### Query Optimization
- Use `limit()` for pagination
- Implement cursor-based pagination with `startAfter()`
- Cache frequently accessed data
- Use real-time listeners sparingly

### Data Structure Best Practices
- Denormalize data for read performance
- Keep document sizes under 1MB
- Use subcollections for large datasets
- Implement data archiving for old gameweeks

## 🚀 Deployment Checklist

### Development Setup
1. ✅ Create Firebase project
2. ✅ Configure authentication
3. ✅ Set up Firestore database
4. ✅ Deploy security rules
5. ✅ Create required indexes
6. ✅ Seed initial data

### Production Setup
1. ⏳ Configure production Firebase project
2. ⏳ Set up automated backups
3. ⏳ Configure monitoring and alerts
4. ⏳ Implement data retention policies
5. ⏳ Set up CI/CD for database updates

## 🔧 Maintenance

### Regular Tasks
- Monitor query performance
- Review and optimize indexes
- Clean up old data
- Update security rules as needed
- Backup critical data

### Scaling Considerations
- Implement data sharding for large datasets
- Use Cloud Functions for complex operations
- Consider read replicas for high-traffic queries
- Monitor and optimize costs

## 📚 Additional Resources

- [Firebase Firestore Documentation](https://firebase.google.com/docs/firestore)
- [Security Rules Guide](https://firebase.google.com/docs/firestore/security/get-started)
- [Performance Best Practices](https://firebase.google.com/docs/firestore/best-practices)
- [Pricing Calculator](https://firebase.google.com/pricing)
